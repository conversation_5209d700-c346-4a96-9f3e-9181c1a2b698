# MQTT中台数据库脚本修复报告

## 修复概述

本次修复主要解决了SQL脚本与EMQX插件实体类之间的对应关系问题，以及潜在的死锁问题。

## 修复的主要问题

### 1. 字段映射不一致问题

#### MqttInstance表修复：
- **删除重复字段**：移除了重复的 `LastHeartbeat` 字段，保留 `LastHeartbeatTime`
- **修复字段类型**：将 `ConfigJson` 从 `varchar(255)` 改为 `text` 类型
- **统一默认值**：将 `MaxConnections` 默认值从 10000 改为 1000000，与实体类保持一致

#### MqttClient表修复：
- **删除重复字段**：
  - 移除 `LastConnectTime`，保留 `ConnectedAt`
  - 移除 `LastDisconnectTime`，保留 `DisconnectedAt`  
  - 移除 `ConnectedTime` 和 `DisconnectedTime`，避免重复
  - 移除 `LastActiveTime`，保留 `LastActivity`
  - 移除 `MessageSentCount` 和 `MessageReceivedCount`，保留 `MessagesSent` 和 `MessagesReceived`

#### MqttTopic表修复：
- **删除重复字段**：
  - 移除 `IsRetained`，保留 `RetainMessage`
  - 移除 `SubscriberCount`，保留 `SubscriptionCount`
  - 移除 `BytesCount`，保留 `ByteCount`

#### MqttClientAuth表修复：
- **字段长度统一**：将 `Username` 长度从 255 改为 128，与实体类一致
- **字段长度统一**：将 `Salt` 长度从 100 改为 64，与实体类一致
- **数据类型统一**：将 `IsDelete` 从 `tinyint(1)` 改为 `bit(1)`，与其他表保持一致

#### MqttSubscription表修复：
- **删除重复字段**：
  - 移除 `SubscribeTime`，保留 `SubscribedAt`
  - 移除 `UnsubscribeTime`，保留 `UnsubscribedAt`
  - 移除 `LastActivityTime`，保留 `LastActivity`
  - 移除 `MessageDropped`，保留 `MessagesDropped`

### 2. 索引优化避免死锁

#### 索引顺序优化原则：
1. **外键索引优先**：将外键相关的索引放在前面，减少锁等待时间
2. **复合索引优化**：调整复合索引中字段的顺序，提高查询效率
3. **唯一索引后置**：将唯一索引放在普通索引之后，避免冲突

#### 具体优化：

**MqttInstance表**：
- 调整索引顺序：`IX_mqtt_instance_type` 在 `IX_mqtt_instance_status` 之前

**MqttClient表**：
- 将 `IX_mqtt_client_instance` 索引提前到第一位
- 将 `IX_mqtt_client_device` 索引提前，因为经常用于查询

**MqttTopic表**：
- 将 `IX_mqtt_topic_instance` 索引提前到第一位

**MqttClientAuth表**：
- 将外键索引 `IX_mqtt_client_auth_instance` 和 `IX_mqtt_client_auth_client_ref` 提前
- 将唯一索引 `IX_mqtt_client_auth_client_instance` 放在外键索引之后

**MqttSubscription表**：
- 将所有外键索引提前：`IX_mqtt_subscription_instance`、`IX_mqtt_subscription_client_ref`、`IX_mqtt_subscription_topic_id`
- 将唯一索引 `IX_mqtt_subscription_client_topic_name` 放在外键索引之后

### 3. 数据类型统一

- 确保所有 `bit(1)` 类型字段使用统一的默认值格式 `b'0'` 或 `b'1'`
- 统一字符串字段的长度定义，与实体类中的 `MaxLength` 特性保持一致
- 统一数值字段的默认值，与实体类中的默认值保持一致

## 修复后的优势

### 1. 消除数据不一致风险
- 删除重复字段避免了数据同步问题
- 统一字段类型和长度避免了数据截断或类型转换错误

### 2. 提高并发性能
- 优化的索引顺序减少了死锁发生的概率
- 外键索引优先策略提高了关联查询的性能

### 3. 提高维护性
- SQL脚本与实体类完全对应，便于后续维护
- 清晰的字段命名和注释，提高代码可读性

## 验证方法

使用提供的 `validate_schema.sql` 脚本可以验证：
1. 表结构是否正确创建
2. 字段定义是否与实体类匹配
3. 索引是否按预期创建
4. 外键约束是否正确设置
5. 默认数据是否成功插入

## 建议

1. **测试环境验证**：在测试环境中运行修复后的脚本，确保没有遗漏的问题
2. **性能测试**：在高并发场景下测试索引优化的效果
3. **数据迁移**：如果已有数据，需要制定相应的数据迁移策略
4. **监控设置**：建议设置数据库死锁监控，及时发现潜在问题

## 修复文件

- `mqtt_middleware_platform_fixed.sql` - 修复后的主脚本
- `validate_schema.sql` - 验证脚本
- `SCHEMA_FIX_REPORT.md` - 本修复报告
