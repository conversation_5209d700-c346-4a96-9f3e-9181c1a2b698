// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Plugin.EMQX.Entity;
using Admin.NET.Plugin.EMQX.Enum;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Mapster;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Admin.NET.Plugin.EMQX.Service;

/// <summary>
/// MQTT客户端服务
/// </summary>
[ApiDescriptionSettings("Plugin-EMQX", Name = "MqttClient", Order = 200)]
public class MqttClientService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MqttClient> _rep;
    private readonly SqlSugarRepository<MqttInstance> _instanceRep;
    private readonly ILogger<MqttClientService> _logger;

    public MqttClientService(
        SqlSugarRepository<MqttClient> rep,
        SqlSugarRepository<MqttInstance> instanceRep,
        ILogger<MqttClientService> logger)
    {
        _rep = rep;
        _instanceRep = instanceRep;
        _logger = logger;
    }

    /// <summary>
    /// 分页查询MQTT客户端
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<MqttClientPageOutput>> GetPageAsync(PageMqttClientInput input)
    {
        var query = _rep.AsQueryable()
            .LeftJoin<MqttInstance>((c, i) => c.InstanceId == i.Id)
            .WhereIF(input.InstanceId.HasValue, (c, i) => c.InstanceId == input.InstanceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.ClientId), (c, i) => c.ClientId.Contains(input.ClientId!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceName), (c, i) => c.DeviceName.Contains(input.DeviceName!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ProductKey), (c, i) => c.ProductKey.Contains(input.ProductKey!))
            .WhereIF(input.Status.HasValue, (c, i) => c.Status == input.Status)
            .WhereIF(input.IsOnline.HasValue, (c, i) => c.IsOnline == input.IsOnline)
            .WhereIF(input.IsEnabled.HasValue, (c, i) => c.IsEnabled == input.IsEnabled)
            .WhereIF(!string.IsNullOrWhiteSpace(input.IpAddress), (c, i) => c.IpAddress.Contains(input.IpAddress!))
            .Select((c, i) => new MqttClientPageOutput
            {
                Id = c.Id,
                InstanceId = c.InstanceId,
                InstanceName = i.InstanceName,
                ClientId = c.ClientId,
                DeviceName = c.DeviceName,
                GroupId = c.GroupId,
                ProductKey = c.ProductKey,
                IpAddress = c.IpAddress,
                Port = c.Port ?? 0,
                Status = c.Status,
                ProtocolVersion = c.ProtocolVersion,
                ConnectedTime = c.ConnectedTime,
                DisconnectedTime = c.DisconnectedTime,
                LastActiveTime = c.LastActiveTime,
                MessagesSent = c.MessagesSent,
                MessagesReceived = c.MessagesReceived,
                BytesSent = c.BytesSent,
                BytesReceived = c.BytesReceived,
                SubscriptionCount = c.SubscriptionCount,
                DeviceType = c.DeviceType,
                DeviceVersion = c.DeviceVersion,
                IsOnline = c.IsOnline,
                IsEnabled = c.IsEnabled,
                CreateTime = c.CreateTime
            })
            .OrderBy(x => x.CreateTime, OrderByType.Desc);

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取MQTT客户端详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<MqttClient> GetDetailAsync([Required] long id)
    {
        var client = await _rep.GetByIdAsync(id);
        if (client == null)
            throw Oops.Oh("客户端不存在");
        return client;
    }

    /// <summary>
    /// 添加MQTT客户端
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task<long> AddAsync(AddMqttClientInput input)
    {
        // 验证实例是否存在
        var instance = await _instanceRep.GetByIdAsync(input.InstanceId);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        // 生成客户端ID
        var clientId = await GenerateClientIdAsync(input.InstanceId, input.DeviceName, input.ProductKey);

        // 检查客户端ID是否重复
        var existClient = await _rep.GetFirstAsync(x => x.ClientId == clientId && x.InstanceId == input.InstanceId);
        if (existClient != null)
            throw Oops.Oh("客户端ID已存在");

        var client = input.Adapt<MqttClient>();
        client.ClientId = clientId;
        client.Status = ClientStatusEnum.Offline;
        client.IsOnline = false;
        client.ConnectedTime = null;
        client.DisconnectedTime = DateTime.Now;
        client.LastActiveTime = DateTime.Now;
        client.MessagesSent = 0;
        client.MessagesReceived = 0;
        client.BytesSent = 0;
        client.BytesReceived = 0;
        client.SubscriptionCount = 0;

        var newClient = await _rep.InsertReturnEntityAsync(client);
        return newClient.Id;
    }

    /// <summary>
    /// 更新MQTT客户端
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task UpdateAsync(UpdateMqttClientInput input)
    {
        var client = await _rep.GetByIdAsync(input.Id);
        if (client == null)
            throw Oops.Oh("客户端不存在");

        // 如果设备名称或产品Key发生变化，需要重新生成客户端ID
        if (input.DeviceName != client.DeviceName || input.ProductKey != client.ProductKey)
        {
            var newClientId = await GenerateClientIdAsync(input.InstanceId, input.DeviceName, input.ProductKey);
            
            // 检查新的客户端ID是否重复（排除自己）
            var existClient = await _rep.GetFirstAsync(x => x.ClientId == newClientId && x.InstanceId == input.InstanceId && x.Id != input.Id);
            if (existClient != null)
                throw Oops.Oh("客户端ID已存在");

            client.ClientId = newClientId;
        }

        var updateClient = input.Adapt<MqttClient>();
        updateClient.ClientId = client.ClientId; // 保持客户端ID
        await _rep.UpdateAsync(updateClient);
    }

    /// <summary>
    /// 删除MQTT客户端
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task DeleteAsync(DeleteMqttClientInput input)
    {
        var client = await _rep.GetByIdAsync(input.Id);
        if (client == null)
            throw Oops.Oh("客户端不存在");

        // 检查客户端是否在线
        if (client.IsOnline)
            throw Oops.Oh("客户端正在线，无法删除");

        await _rep.DeleteAsync(client);
    }

    /// <summary>
    /// 批量删除MQTT客户端
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "BatchDelete")]
    public async Task BatchDeleteAsync(BatchDeleteMqttClientInput input)
    {
        if (input.Ids == null || !input.Ids.Any())
            throw Oops.Oh("请选择要删除的客户端");

        var clients = await _rep.GetListAsync(x => input.Ids.Contains(x.Id));
        var onlineClients = clients.Where(x => x.IsOnline).ToList();
        if (onlineClients.Any())
        {
            var onlineClientNames = string.Join(", ", onlineClients.Select(x => x.DeviceName));
            throw Oops.Oh($"以下客户端正在线，无法删除：{onlineClientNames}");
        }

        await _rep.DeleteByIdsAsync(input.Ids.Cast<dynamic>().ToArray());
    }

    /// <summary>
    /// 启用/禁用MQTT客户端
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "ToggleStatus")]
    public async Task ToggleStatusAsync(ToggleClientStatusInput input)
    {
        var client = await _rep.GetByIdAsync(input.Id);
        if (client == null)
            throw Oops.Oh("客户端不存在");

        client.IsEnabled = input.IsEnabled;
        if (!input.IsEnabled && client.IsOnline)
        {
            // 禁用时如果在线，设置为离线状态
            client.Status = ClientStatusEnum.Disabled;
            client.IsOnline = false;
            client.DisconnectedTime = DateTime.Now;
        }

        await _rep.UpdateAsync(client);
    }

    /// <summary>
    /// 更新客户端连接状态
    /// </summary>
    /// <param name="clientId"></param>
    /// <param name="instanceId"></param>
    /// <param name="status"></param>
    /// <param name="ipAddress"></param>
    /// <param name="port"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "UpdateConnectionStatus")]
    public async Task UpdateConnectionStatusAsync(
        [Required] string clientId,
        [Required] long instanceId,
        [Required] ClientStatusEnum status,
        string? ipAddress = null,
        int? port = null)
    {
        var client = await _rep.GetFirstAsync(x => x.ClientId == clientId && x.InstanceId == instanceId);
        if (client == null)
            throw Oops.Oh("客户端不存在");

        client.Status = status;
        client.IsOnline = status == ClientStatusEnum.Online;
        client.LastActiveTime = DateTime.Now;

        if (!string.IsNullOrWhiteSpace(ipAddress))
            client.IpAddress = ipAddress;
        if (port.HasValue)
            client.Port = port.Value;

        if (status == ClientStatusEnum.Online)
        {
            client.ConnectedTime = DateTime.Now;
        }
        else if (status == ClientStatusEnum.Offline)
        {
            client.DisconnectedTime = DateTime.Now;
        }

        await _rep.UpdateAsync(client);
    }

    /// <summary>
    /// 更新客户端统计信息
    /// </summary>
    /// <param name="clientId"></param>
    /// <param name="instanceId"></param>
    /// <param name="messagesSent"></param>
    /// <param name="messagesReceived"></param>
    /// <param name="bytesSent"></param>
    /// <param name="bytesReceived"></param>
    /// <param name="subscriptionCount"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "UpdateStatistics")]
    public async Task UpdateStatisticsAsync(
        [Required] string clientId,
        [Required] long instanceId,
        long messagesSent,
        long messagesReceived,
        long bytesSent,
        long bytesReceived,
        int subscriptionCount)
    {
        var client = await _rep.GetFirstAsync(x => x.ClientId == clientId && x.InstanceId == instanceId);
        if (client == null)
            throw Oops.Oh("客户端不存在");

        client.MessagesSent = messagesSent;
        client.MessagesReceived = messagesReceived;
        client.BytesSent = bytesSent;
        client.BytesReceived = bytesReceived;
        client.SubscriptionCount = subscriptionCount;
        client.LastActiveTime = DateTime.Now;

        await _rep.UpdateAsync(client);
    }

    /// <summary>
    /// 根据实例ID获取客户端列表
    /// </summary>
    /// <param name="instanceId"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetByInstance")]
    public async Task<List<MqttClientSelectOutput>> GetByInstanceAsync([Required] long instanceId)
    {
        var clients = await _rep.AsQueryable()
            .Where(x => x.InstanceId == instanceId && x.IsEnabled)
            .OrderBy(x => x.DeviceName)
            .Select(x => new MqttClientSelectOutput
            {
                Id = x.Id,
                ClientId = x.ClientId,
                DeviceName = x.DeviceName,
                ProductKey = x.ProductKey,
                Status = x.Status,
                IsOnline = x.IsOnline
            })
            .ToListAsync();

        return clients;
    }

    /// <summary>
    /// 生成阿里云兼容的客户端ID
    /// </summary>
    /// <param name="instanceId"></param>
    /// <param name="deviceName"></param>
    /// <param name="productKey"></param>
    /// <returns></returns>
    private async Task<string> GenerateClientIdAsync(long instanceId, string deviceName, string? productKey = null)
    {
        var instance = await _instanceRep.GetByIdAsync(instanceId);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        // 如果配置了阿里云产品Key，使用阿里云格式
        if (!string.IsNullOrWhiteSpace(instance.AliyunProductKey) && !string.IsNullOrWhiteSpace(productKey))
        {
            // 阿里云客户端ID格式：{ProductKey}.{DeviceName}|securemode=3,signmethod=hmacsha1|
            return $"{productKey}.{deviceName}|securemode=3,signmethod=hmacsha1|";
        }

        // 使用自定义格式
        var prefix = !string.IsNullOrWhiteSpace(instance.DeviceIdPrefix) ? instance.DeviceIdPrefix : "device";
        return $"{prefix}_{deviceName}_{instanceId}";
    }

    /// <summary>
    /// 验证设备名称格式
    /// </summary>
    /// <param name="deviceName"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "ValidateDeviceName")]
    public bool ValidateDeviceName([Required] string deviceName)
    {
        // 阿里云设备名称规则：4-32位字符，支持英文字母、数字、下划线、连字符、点号
        var pattern = @"^[a-zA-Z0-9_.-]{4,32}$";
        return Regex.IsMatch(deviceName, pattern);
    }

    /// <summary>
    /// 验证产品Key格式
    /// </summary>
    /// <param name="productKey"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "ValidateProductKey")]
    public bool ValidateProductKey([Required] string productKey)
    {
        // 阿里云产品Key规则：11位字符，支持英文字母和数字
        var pattern = @"^[a-zA-Z0-9]{11}$";
        return Regex.IsMatch(productKey, pattern);
    }
}

#region 输入输出模型

/// <summary>
/// 分页查询MQTT客户端输入
/// </summary>
public class PageMqttClientInput : BasePageInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    public long? InstanceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 产品Key
    /// </summary>
    public string? ProductKey { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    public ClientStatusEnum? Status { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool? IsOnline { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }

    /// <summary>
    /// IP地址
    /// </summary>
    public string? IpAddress { get; set; }
}

/// <summary>
/// 添加MQTT客户端输入
/// </summary>
public class AddMqttClientInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    [Required(ErrorMessage = "设备名称不能为空")]
    [MaxLength(64, ErrorMessage = "设备名称长度不能超过64个字符")]
    public string DeviceName { get; set; }

    /// <summary>
    /// 组ID
    /// </summary>
    [MaxLength(128, ErrorMessage = "组ID长度不能超过128个字符")]
    public string? GroupId { get; set; }

    /// <summary>
    /// 产品Key
    /// </summary>
    [MaxLength(50, ErrorMessage = "产品Key长度不能超过50个字符")]
    public string? ProductKey { get; set; }

    /// <summary>
    /// 设备密钥
    /// </summary>
    [MaxLength(256, ErrorMessage = "设备密钥长度不能超过256个字符")]
    public string? DeviceSecret { get; set; }

    /// <summary>
    /// 协议版本
    /// </summary>
    [MaxLength(10, ErrorMessage = "协议版本长度不能超过10个字符")]
    public string? ProtocolVersion { get; set; } = "3.1.1";

    /// <summary>
    /// Keep Alive时间（秒）
    /// </summary>
    [Range(0, 65535, ErrorMessage = "Keep Alive时间范围必须在0-65535之间")]
    public int KeepAlive { get; set; } = 60;

    /// <summary>
    /// Clean Session标志
    /// </summary>
    public bool CleanSession { get; set; } = true;

    /// <summary>
    /// 设备类型
    /// </summary>
    [MaxLength(50, ErrorMessage = "设备类型长度不能超过50个字符")]
    public string? DeviceType { get; set; }

    /// <summary>
    /// 设备版本
    /// </summary>
    [MaxLength(50, ErrorMessage = "设备版本长度不能超过50个字符")]
    public string? DeviceVersion { get; set; }

    /// <summary>
    /// 设备标签
    /// </summary>
    public string? DeviceTags { get; set; }

    /// <summary>
    /// 地理位置（经度）
    /// </summary>
    public decimal? Longitude { get; set; }

    /// <summary>
    /// 地理位置（纬度）
    /// </summary>
    public decimal? Latitude { get; set; }

    /// <summary>
    /// 地理位置（海拔）
    /// </summary>
    public decimal? Altitude { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 认证方式
    /// </summary>
    public AuthModeEnum AuthMode { get; set; } = AuthModeEnum.Username;

    /// <summary>
    /// 扩展属性
    /// </summary>
    public string? ExtendedProperties { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 更新MQTT客户端输入
/// </summary>
public class UpdateMqttClientInput : AddMqttClientInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 删除MQTT客户端输入
/// </summary>
public class DeleteMqttClientInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 批量删除MQTT客户端输入
/// </summary>
public class BatchDeleteMqttClientInput
{
    /// <summary>
    /// 主键ID集合
    /// </summary>
    [Required(ErrorMessage = "主键ID集合不能为空")]
    public List<long> Ids { get; set; }
}

/// <summary>
/// 切换客户端状态输入
/// </summary>
public class ToggleClientStatusInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}

/// <summary>
/// MQTT客户端分页输出
/// </summary>
public class MqttClientPageOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 实例ID
    /// </summary>
    public long InstanceId { get; set; }

    /// <summary>
    /// 实例名称
    /// </summary>
    public string InstanceName { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 组ID
    /// </summary>
    public string? GroupId { get; set; }

    /// <summary>
    /// 产品Key
    /// </summary>
    public string? ProductKey { get; set; }

    /// <summary>
    /// IP地址
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// 端口
    /// </summary>
    public int Port { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    public ClientStatusEnum Status { get; set; }

    /// <summary>
    /// 协议版本
    /// </summary>
    public string? ProtocolVersion { get; set; }

    /// <summary>
    /// 连接时间
    /// </summary>
    public DateTime? ConnectedTime { get; set; }

    /// <summary>
    /// 断开时间
    /// </summary>
    public DateTime? DisconnectedTime { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime? LastActiveTime { get; set; }

    /// <summary>
    /// 发送消息数
    /// </summary>
    public long MessagesSent { get; set; }

    /// <summary>
    /// 接收消息数
    /// </summary>
    public long MessagesReceived { get; set; }

    /// <summary>
    /// 发送字节数
    /// </summary>
    public long BytesSent { get; set; }

    /// <summary>
    /// 接收字节数
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 订阅数量
    /// </summary>
    public int SubscriptionCount { get; set; }

    /// <summary>
    /// 设备类型
    /// </summary>
    public string? DeviceType { get; set; }

    /// <summary>
    /// 设备版本
    /// </summary>
    public string? DeviceVersion { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}

/// <summary>
/// MQTT客户端选择输出
/// </summary>
public class MqttClientSelectOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 产品Key
    /// </summary>
    public string? ProductKey { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    public ClientStatusEnum Status { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }
}

#endregion