// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Plugin.EMQX.Enum;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Plugin.EMQX.Entity;

/// <summary>
/// MQTT客户端表
/// </summary>
[SugarTable("mqtt_client", "MQTT客户端表")]
[SugarIndex("idx_mqtt_client_id", nameof(ClientId), OrderByType.Asc)]
[SugarIndex("idx_mqtt_client_instance", nameof(InstanceId), OrderByType.Asc)]
[SugarIndex("idx_mqtt_client_status", nameof(Status), OrderByType.Asc)]
[SugarIndex("idx_mqtt_client_device_name", nameof(DeviceName), OrderByType.Asc)]
[SugarIndex("idx_mqtt_client_group_id", nameof(GroupId), OrderByType.Asc)]
public class MqttClient : EntityBaseDel
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [SugarColumn(ColumnDescription = "实例ID")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 客户端ID（完整格式，如：GID_TestGroup@@@DeviceName）
    /// </summary>
    [SugarColumn(ColumnDescription = "客户端ID", Length = 128)]
    [Required, MaxLength(128)]
    public string ClientId { get; set; }

    /// <summary>
    /// 设备名称（从ClientId解析出来）
    /// </summary>
    [SugarColumn(ColumnDescription = "设备名称", Length = 64)]
    [Required, MaxLength(64)]
    public string DeviceName { get; set; }

    /// <summary>
    /// 组ID（从ClientId解析出来，如：GID_TestGroup）
    /// </summary>
    [SugarColumn(ColumnDescription = "组ID", Length = 64, IsNullable = true)]
    [MaxLength(64)]
    public string? GroupId { get; set; }

    /// <summary>
    /// 产品Key（阿里云兼容）
    /// </summary>
    [SugarColumn(ColumnDescription = "产品Key", Length = 64, IsNullable = true)]
    [MaxLength(64)]
    public string? ProductKey { get; set; }

    /// <summary>
    /// 设备密钥（阿里云兼容）
    /// </summary>
    [SugarColumn(ColumnDescription = "设备密钥", Length = 128, IsNullable = true)]
    [MaxLength(128)]
    public string? DeviceSecret { get; set; }

    /// <summary>
    /// 客户端IP地址
    /// </summary>
    [SugarColumn(ColumnDescription = "客户端IP地址", Length = 45, IsNullable = true)]
    [MaxLength(45)]
    public string? IpAddress { get; set; }

    /// <summary>
    /// 客户端端口
    /// </summary>
    [SugarColumn(ColumnDescription = "客户端端口", IsNullable = true)]
    public int? Port { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    [SugarColumn(ColumnDescription = "连接状态")]
    public ClientStatusEnum Status { get; set; } = ClientStatusEnum.Connected;

    /// <summary>
    /// 协议版本
    /// </summary>
    [SugarColumn(ColumnDescription = "协议版本", Length = 16, IsNullable = true)]
    [MaxLength(16)]
    public string? ProtocolVersion { get; set; }

    /// <summary>
    /// Keep Alive时间（秒）
    /// </summary>
    [SugarColumn(ColumnDescription = "Keep Alive时间", IsNullable = true)]
    public int? KeepAlive { get; set; }

    /// <summary>
    /// Clean Session标志
    /// </summary>
    [SugarColumn(ColumnDescription = "Clean Session标志")]
    public bool CleanSession { get; set; } = true;

    /// <summary>
    /// 连接时间
    /// </summary>
    [SugarColumn(ColumnDescription = "连接时间", IsNullable = true)]
    public DateTime? ConnectedAt { get; set; }

    /// <summary>
    /// 断开时间
    /// </summary>
    [SugarColumn(ColumnDescription = "断开时间", IsNullable = true)]
    public DateTime? DisconnectedAt { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后活动时间", IsNullable = true)]
    public DateTime? LastActivity { get; set; }

    /// <summary>
    /// 最后活跃时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后活跃时间", IsNullable = true)]
    public DateTime? LastActiveTime { get; set; }

    /// <summary>
    /// 断开连接时间
    /// </summary>
    [SugarColumn(ColumnDescription = "断开连接时间", IsNullable = true)]
    public DateTime? DisconnectedTime { get; set; }

    /// <summary>
    /// 连接时间
    /// </summary>
    [SugarColumn(ColumnDescription = "连接时间", IsNullable = true)]
    public DateTime? ConnectedTime { get; set; }

    /// <summary>
    /// 发送消息数
    /// </summary>
    [SugarColumn(ColumnDescription = "发送消息数")]
    public long MessagesSent { get; set; } = 0;

    /// <summary>
    /// 接收消息数
    /// </summary>
    [SugarColumn(ColumnDescription = "接收消息数")]
    public long MessagesReceived { get; set; } = 0;

    /// <summary>
    /// 发送字节数
    /// </summary>
    [SugarColumn(ColumnDescription = "发送字节数")]
    public long BytesSent { get; set; } = 0;

    /// <summary>
    /// 接收字节数
    /// </summary>
    [SugarColumn(ColumnDescription = "接收字节数")]
    public long BytesReceived { get; set; } = 0;

    /// <summary>
    /// 订阅数量
    /// </summary>
    [SugarColumn(ColumnDescription = "订阅数量")]
    public int SubscriptionCount { get; set; } = 0;

    /// <summary>
    /// 最大订阅数量
    /// </summary>
    [SugarColumn(ColumnDescription = "最大订阅数量")]
    public int MaxSubscriptions { get; set; } = 100;

    /// <summary>
    /// 设备类型
    /// </summary>
    [SugarColumn(ColumnDescription = "设备类型", Length = 32, IsNullable = true)]
    [MaxLength(32)]
    public string? DeviceType { get; set; }

    /// <summary>
    /// 设备版本
    /// </summary>
    [SugarColumn(ColumnDescription = "设备版本", Length = 32, IsNullable = true)]
    [MaxLength(32)]
    public string? DeviceVersion { get; set; }

    /// <summary>
    /// 设备标签（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "设备标签", ColumnDataType = "text", IsNullable = true)]
    public string? DeviceTags { get; set; }

    /// <summary>
    /// 地理位置信息（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "地理位置信息", ColumnDataType = "text", IsNullable = true)]
    public string? LocationInfo { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    [SugarColumn(ColumnDescription = "是否在线")]
    public bool IsOnline { get; set; } = false;

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 认证方式
    /// </summary>
    [SugarColumn(ColumnDescription = "认证方式")]
    public AuthModeEnum AuthMode { get; set; } = AuthModeEnum.Username;

    /// <summary>
    /// 客户端类型
    /// </summary>
    [SugarColumn(ColumnDescription = "客户端类型", Length = 32, IsNullable = true, ColumnName = "ClientType")]
    [MaxLength(32)]
    public string? ClientType { get; set; }

    /// <summary>
    /// 最后错误信息
    /// </summary>
    [SugarColumn(ColumnDescription = "最后错误信息", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string? LastError { get; set; }

    /// <summary>
    /// 扩展属性（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展属性", ColumnDataType = "text", IsNullable = true)]
    public string? ExtendedProperties { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string? Remark { get; set; }

    /// <summary>
    /// 所属实例
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(InstanceId))]
    public MqttInstance? Instance { get; set; }

    /// <summary>
    /// 订阅列表
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(MqttSubscription.ClientId), nameof(ClientId))]
    public List<MqttSubscription>? Subscriptions { get; set; }

    /// <summary>
    /// 认证配置
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MqttClientAuth.ClientId), nameof(ClientId))]
    public MqttClientAuth? AuthConfig { get; set; }
}