/*
 验证修复后的MQTT中台数据库脚本
 用于检查表结构是否正确创建，字段是否与实体类对应
*/

-- 验证表是否存在
SELECT 'Checking table existence...' as Status;

SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_COLLATION
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('mqtt_instance', 'mqtt_client', 'mqtt_topic', 'mqtt_client_auth', 'mqtt_subscription')
ORDER BY TABLE_NAME;

-- 验证mqtt_instance表结构
SELECT 'Checking mqtt_instance table structure...' as Status;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'mqtt_instance'
ORDER BY ORDINAL_POSITION;

-- 验证mqtt_client表结构
SELECT 'Checking mqtt_client table structure...' as Status;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'mqtt_client'
ORDER BY ORDINAL_POSITION;

-- 验证mqtt_topic表结构
SELECT 'Checking mqtt_topic table structure...' as Status;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'mqtt_topic'
ORDER BY ORDINAL_POSITION;

-- 验证mqtt_client_auth表结构
SELECT 'Checking mqtt_client_auth table structure...' as Status;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'mqtt_client_auth'
ORDER BY ORDINAL_POSITION;

-- 验证mqtt_subscription表结构
SELECT 'Checking mqtt_subscription table structure...' as Status;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'mqtt_subscription'
ORDER BY ORDINAL_POSITION;

-- 验证索引结构
SELECT 'Checking indexes...' as Status;

SELECT 
    TABLE_NAME,
    INDEX_NAME,
    NON_UNIQUE,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('mqtt_instance', 'mqtt_client', 'mqtt_topic', 'mqtt_client_auth', 'mqtt_subscription')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 验证外键约束
SELECT 'Checking foreign key constraints...' as Status;

SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME,
    DELETE_RULE,
    UPDATE_RULE
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
AND REFERENCED_TABLE_NAME IS NOT NULL
AND TABLE_NAME IN ('mqtt_instance', 'mqtt_client', 'mqtt_topic', 'mqtt_client_auth', 'mqtt_subscription')
ORDER BY TABLE_NAME, CONSTRAINT_NAME;

-- 验证默认数据是否插入成功
SELECT 'Checking default data...' as Status;

SELECT 
    Id,
    InstanceName,
    InstanceCode,
    InstanceType,
    ServerHost,
    ServerPort,
    IsEnabled,
    CreateTime
FROM mqtt_instance
WHERE Id IN (1, 2);

SELECT 'Validation completed!' as Status;
