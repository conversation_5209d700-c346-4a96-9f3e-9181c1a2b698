// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.ComponentModel;

namespace Admin.NET.Plugin.EMQX.Enum;

/// <summary>
/// MQTT订阅状态枚举
/// </summary>
[Description("MQTT订阅状态")]
public enum SubscriptionStatusEnum
{
    /// <summary>
    /// 未订阅
    /// </summary>
    [Description("未订阅")]
    Unsubscribed = 0,

    /// <summary>
    /// 已订阅
    /// </summary>
    [Description("已订阅")]
    Subscribed = 1,

    /// <summary>
    /// 活跃
    /// </summary>
    [Description("活跃")]
    Active = 2,

    /// <summary>
    /// 订阅中
    /// </summary>
    [Description("订阅中")]
    Subscribing = 3,

    /// <summary>
    /// 取消订阅中
    /// </summary>
    [Description("取消订阅中")]
    Unsubscribing = 4,

    /// <summary>
    /// 订阅失败
    /// </summary>
    [Description("订阅失败")]
    Failed = 5,

    /// <summary>
    /// 已禁用
    /// </summary>
    [Description("已禁用")]
    Disabled = 6,

    /// <summary>
    /// 暂停
    /// </summary>
    [Description("暂停")]
    Paused = 5,

    /// <summary>
    /// 过期
    /// </summary>
    [Description("过期")]
    Expired = 6
}