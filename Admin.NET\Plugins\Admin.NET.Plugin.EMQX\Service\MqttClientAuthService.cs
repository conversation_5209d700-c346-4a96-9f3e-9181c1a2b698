// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Plugin.EMQX.Entity;
using Admin.NET.Plugin.EMQX.Enum;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Mapster;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace Admin.NET.Plugin.EMQX.Service;

/// <summary>
/// MQTT客户端认证服务
/// </summary>
[ApiDescriptionSettings("Plugin-EMQX", Name = "MqttClientAuth", Order = 300)]
public class MqttClientAuthService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MqttClientAuth> _rep;
    private readonly SqlSugarRepository<MqttInstance> _instanceRep;
    private readonly SqlSugarRepository<MqttClient> _clientRep;
    private readonly ILogger<MqttClientAuthService> _logger;

    public MqttClientAuthService(
        SqlSugarRepository<MqttClientAuth> rep,
        SqlSugarRepository<MqttInstance> instanceRep,
        SqlSugarRepository<MqttClient> clientRep,
        ILogger<MqttClientAuthService> logger)
    {
        _rep = rep;
        _instanceRep = instanceRep;
        _clientRep = clientRep;
        _logger = logger;
    }

    /// <summary>
    /// 分页查询MQTT客户端认证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<MqttClientAuthPageOutput>> GetPageAsync(PageMqttClientAuthInput input)
    {
        var query = _rep.AsQueryable()
            .LeftJoin<MqttInstance>((a, i) => a.InstanceId == i.Id)
            .LeftJoin<MqttClient>((a, i, c) => a.ClientId == c.ClientId && a.InstanceId == c.InstanceId)
            .WhereIF(input.InstanceId.HasValue, (a, i, c) => a.InstanceId == input.InstanceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.ClientId), (a, i, c) => a.ClientId.Contains(input.ClientId!))
            .WhereIF(input.AuthMode.HasValue, (a, i, c) => a.AuthMode == input.AuthMode)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Username), (a, i, c) => a.Username.Contains(input.Username!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AccessKeyId), (a, i, c) => a.AccessKeyId.Contains(input.AccessKeyId!))
            .WhereIF(input.IsEnabled.HasValue, (a, i, c) => a.IsEnabled == input.IsEnabled)
            .WhereIF(input.IsLocked.HasValue, (a, i, c) => a.IsLocked == input.IsLocked)
            .Select((a, i, c) => new MqttClientAuthPageOutput
            {
                Id = a.Id,
                InstanceId = a.InstanceId,
                InstanceName = i.InstanceName,
                ClientId = a.ClientId,
                DeviceName = c.DeviceName,
                AuthMode = a.AuthMode,
                Username = a.Username,
                AccessKeyId = a.AccessKeyId,
                SignMethod = a.SignMethod,
                IsEnabled = a.IsEnabled,
                ExpireTime = a.ExpireTime,
                LastAuthTime = a.LastAuthTime,
                SuccessCount = a.SuccessCount,
                FailedCount = a.FailedCount,
                IsLocked = a.IsLocked,
                LockTime = a.LockTime,
                LockReason = a.LockReason,
                MaxConnections = a.MaxConnections,
                CurrentConnections = a.CurrentConnections,
                CreateTime = a.CreateTime
            })
            .OrderBy(x => x.CreateTime, OrderByType.Desc);

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取MQTT客户端认证详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<MqttClientAuth> GetDetailAsync([Required] long id)
    {
        var auth = await _rep.GetByIdAsync(id);
        if (auth == null)
            throw Oops.Oh("认证配置不存在");
        return auth;
    }

    /// <summary>
    /// 添加MQTT客户端认证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task<long> AddAsync(AddMqttClientAuthInput input)
    {
        // 验证实例是否存在
        var instance = await _instanceRep.GetByIdAsync(input.InstanceId);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        // 检查客户端认证是否已存在
        var existAuth = await _rep.GetFirstAsync(x => x.ClientId == input.ClientId && x.InstanceId == input.InstanceId);
        if (existAuth != null)
            throw Oops.Oh("该客户端的认证配置已存在");

        var auth = input.Adapt<MqttClientAuth>();
        
        // 处理密码加密
        if (!string.IsNullOrWhiteSpace(input.Password))
        {
            auth.PasswordSalt = GenerateSalt();
            auth.Password = HashPassword(input.Password, auth.PasswordSalt);
        }

        // 处理AccessKey Secret加密
        if (!string.IsNullOrWhiteSpace(input.AccessKeySecret))
        {
            auth.AccessKeySecret = EncryptAccessKeySecret(input.AccessKeySecret);
        }

        auth.SuccessCount = 0;
        auth.FailedCount = 0;
        auth.IsLocked = false;
        auth.CurrentConnections = 0;

        var newAuth = await _rep.InsertReturnEntityAsync(auth);
        return newAuth.Id;
    }

    /// <summary>
    /// 更新MQTT客户端认证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task UpdateAsync(UpdateMqttClientAuthInput input)
    {
        var auth = await _rep.GetByIdAsync(input.Id);
        if (auth == null)
            throw Oops.Oh("认证配置不存在");

        var updateAuth = input.Adapt<MqttClientAuth>();
        
        // 如果密码有变化，重新加密
        if (!string.IsNullOrWhiteSpace(input.Password) && input.Password != auth.Password)
        {
            updateAuth.PasswordSalt = GenerateSalt();
            updateAuth.Password = HashPassword(input.Password, updateAuth.PasswordSalt);
        }
        else
        {
            updateAuth.Password = auth.Password;
            updateAuth.PasswordSalt = auth.PasswordSalt;
        }

        // 如果AccessKey Secret有变化，重新加密
        if (!string.IsNullOrWhiteSpace(input.AccessKeySecret) && input.AccessKeySecret != auth.AccessKeySecret)
        {
            updateAuth.AccessKeySecret = EncryptAccessKeySecret(input.AccessKeySecret);
        }
        else
        {
            updateAuth.AccessKeySecret = auth.AccessKeySecret;
        }

        await _rep.UpdateAsync(updateAuth);
    }

    /// <summary>
    /// 删除MQTT客户端认证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task DeleteAsync(DeleteMqttClientAuthInput input)
    {
        var auth = await _rep.GetByIdAsync(input.Id);
        if (auth == null)
            throw Oops.Oh("认证配置不存在");

        await _rep.DeleteAsync(auth);
    }

    /// <summary>
    /// 批量删除MQTT客户端认证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "BatchDelete")]
    public async Task BatchDeleteAsync(BatchDeleteMqttClientAuthInput input)
    {
        if (input.Ids == null || !input.Ids.Any())
            throw Oops.Oh("请选择要删除的认证配置");

        await _rep.DeleteByIdsAsync(input.Ids.Cast<dynamic>().ToArray());
    }

    /// <summary>
    /// 启用/禁用MQTT客户端认证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "ToggleStatus")]
    public async Task ToggleStatusAsync(ToggleAuthStatusInput input)
    {
        var auth = await _rep.GetByIdAsync(input.Id);
        if (auth == null)
            throw Oops.Oh("认证配置不存在");

        auth.IsEnabled = input.IsEnabled;
        await _rep.UpdateAsync(auth);
    }

    /// <summary>
    /// 解锁客户端认证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Unlock")]
    public async Task UnlockAsync(UnlockAuthInput input)
    {
        var auth = await _rep.GetByIdAsync(input.Id);
        if (auth == null)
            throw Oops.Oh("认证配置不存在");

        auth.IsLocked = false;
        auth.LockTime = null;
        auth.LockReason = null;
        auth.FailedCount = 0;

        await _rep.UpdateAsync(auth);
    }

    /// <summary>
    /// 验证客户端认证（EMQX HTTP认证接口）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "ValidateAuth")]
    public async Task<AuthValidationResult> ValidateAuthAsync(ValidateAuthInput input)
    {
        try
        {
            // 根据客户端ID和实例查找认证配置
            var auth = await _rep.GetFirstAsync(x => x.ClientId == input.ClientId && x.InstanceId == input.InstanceId);
            if (auth == null)
            {
                return new AuthValidationResult { IsSuccess = false, Message = "认证配置不存在" };
            }

            // 检查是否启用
            if (!auth.IsEnabled)
            {
                return new AuthValidationResult { IsSuccess = false, Message = "认证已禁用" };
            }

            // 检查是否锁定
            if (auth.IsLocked)
            {
                // 检查锁定是否过期
                if (auth.LockTime.HasValue && DateTime.Now > auth.LockTime.Value.AddMinutes(auth.LockDuration))
                {
                    // 解锁
                    auth.IsLocked = false;
                    auth.LockTime = null;
                    auth.LockReason = null;
                    auth.FailedCount = 0;
                    await _rep.UpdateAsync(auth);
                }
                else
                {
                    return new AuthValidationResult { IsSuccess = false, Message = $"认证已锁定：{auth.LockReason}" };
                }
            }

            // 检查过期时间
            if (auth.ExpireTime.HasValue && DateTime.Now > auth.ExpireTime.Value)
            {
                return new AuthValidationResult { IsSuccess = false, Message = "认证已过期" };
            }

            // 检查IP白名单和黑名单
            if (!CheckIpAccess(input.IpAddress, auth.IpWhitelist, auth.IpBlacklist))
            {
                await RecordAuthFailure(auth, "IP地址不在允许范围内");
                return new AuthValidationResult { IsSuccess = false, Message = "IP地址不在允许范围内" };
            }

            // 检查连接数限制
            if (auth.CurrentConnections >= auth.MaxConnections)
            {
                return new AuthValidationResult { IsSuccess = false, Message = "连接数已达上限" };
            }

            bool isValid = false;
            string errorMessage = "";

            // 根据认证模式进行验证
            switch (auth.AuthMode)
            {
                case AuthModeEnum.Username:
                    isValid = ValidateUsernamePassword(input.Username, input.Password, auth);
                    errorMessage = "用户名或密码错误";
                    break;

                case AuthModeEnum.AliyunSignature:
                    isValid = await ValidateAliyunSignatureAsync(input, auth);
                    errorMessage = "阿里云签名验证失败";
                    break;

                case AuthModeEnum.JWT:
                    isValid = ValidateJwtToken(input.Password, auth);
                    errorMessage = "JWT令牌验证失败";
                    break;

                case AuthModeEnum.Certificate:
                    isValid = ValidateCertificate(input.Certificate, auth);
                    errorMessage = "证书验证失败";
                    break;

                case AuthModeEnum.Anonymous:
                    isValid = true;
                    break;

                default:
                    errorMessage = "不支持的认证模式";
                    break;
            }

            if (isValid)
            {
                // 认证成功
                await RecordAuthSuccess(auth);
                return new AuthValidationResult 
                { 
                    IsSuccess = true, 
                    Message = "认证成功",
                    AllowedTopics = ParseTopicList(auth.AllowedTopics),
                    DeniedTopics = ParseTopicList(auth.DeniedTopics)
                };
            }
            else
            {
                // 认证失败
                await RecordAuthFailure(auth, errorMessage);
                return new AuthValidationResult { IsSuccess = false, Message = errorMessage };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "认证验证过程中发生异常：{Message}", ex.Message);
            return new AuthValidationResult { IsSuccess = false, Message = "认证验证异常" };
        }
    }

    /// <summary>
    /// 计算阿里云签名
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "CalculateAliyunSignature")]
    public async Task<string> CalculateAliyunSignatureAsync(CalculateSignatureInput input)
    {
        var instance = await _instanceRep.GetByIdAsync(input.InstanceId);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        return CalculateAliyunSignature(
            input.ProductKey,
            input.DeviceName,
            input.DeviceSecret,
            input.ClientId,
            input.Timestamp,
            input.SignMethod);
    }

    #region 私有方法

    /// <summary>
    /// 生成盐值
    /// </summary>
    /// <returns></returns>
    private string GenerateSalt()
    {
        var bytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// 哈希密码
    /// </summary>
    /// <param name="password"></param>
    /// <param name="salt"></param>
    /// <returns></returns>
    private string HashPassword(string password, string salt)
    {
        using var sha256 = SHA256.Create();
        var saltedPassword = password + salt;
        var bytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// 加密AccessKey Secret
    /// </summary>
    /// <param name="secret"></param>
    /// <returns></returns>
    private string EncryptAccessKeySecret(string secret)
    {
        // 这里应该使用更安全的加密方式，比如AES
        // 为了演示，这里使用简单的Base64编码
        return Convert.ToBase64String(Encoding.UTF8.GetBytes(secret));
    }

    /// <summary>
    /// 解密AccessKey Secret
    /// </summary>
    /// <param name="encryptedSecret"></param>
    /// <returns></returns>
    private string DecryptAccessKeySecret(string encryptedSecret)
    {
        try
        {
            return Encoding.UTF8.GetString(Convert.FromBase64String(encryptedSecret));
        }
        catch
        {
            return encryptedSecret; // 如果解密失败，返回原值
        }
    }

    /// <summary>
    /// 验证用户名密码
    /// </summary>
    /// <param name="username"></param>
    /// <param name="password"></param>
    /// <param name="auth"></param>
    /// <returns></returns>
    private bool ValidateUsernamePassword(string? username, string? password, MqttClientAuth auth)
    {
        if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
            return false;

        if (auth.Username != username)
            return false;

        var hashedPassword = HashPassword(password, auth.PasswordSalt ?? "");
        return auth.Password == hashedPassword;
    }

    /// <summary>
    /// 验证阿里云签名
    /// </summary>
    /// <param name="input"></param>
    /// <param name="auth"></param>
    /// <returns></returns>
    private async Task<bool> ValidateAliyunSignatureAsync(ValidateAuthInput input, MqttClientAuth auth)
    {
        if (string.IsNullOrWhiteSpace(input.Username) || string.IsNullOrWhiteSpace(input.Password))
            return false;

        // 解析用户名格式：{ProductKey}&{DeviceName}
        var usernameParts = input.Username.Split('&');
        if (usernameParts.Length != 2)
            return false;

        var productKey = usernameParts[0];
        var deviceName = usernameParts[1];

        // 获取设备密钥
        var client = await _clientRep.GetFirstAsync(x => x.ClientId == input.ClientId && x.InstanceId == input.InstanceId);
        if (client == null || string.IsNullOrWhiteSpace(client.DeviceSecret))
            return false;

        // 解析客户端ID获取时间戳
        var timestamp = ExtractTimestampFromClientId(input.ClientId);
        if (string.IsNullOrWhiteSpace(timestamp))
            return false;

        // 计算期望的签名
        var expectedSignature = CalculateAliyunSignature(
            productKey,
            deviceName,
            client.DeviceSecret,
            input.ClientId,
            timestamp,
            auth.SignMethod);

        return input.Password == expectedSignature;
    }

    /// <summary>
    /// 计算阿里云签名
    /// </summary>
    /// <param name="productKey"></param>
    /// <param name="deviceName"></param>
    /// <param name="deviceSecret"></param>
    /// <param name="clientId"></param>
    /// <param name="timestamp"></param>
    /// <param name="signMethod"></param>
    /// <returns></returns>
    private string CalculateAliyunSignature(
        string productKey,
        string deviceName,
        string deviceSecret,
        string clientId,
        string timestamp,
        SignMethodEnum signMethod)
    {
        // 构造签名内容
        var content = $"clientId{clientId}deviceName{deviceName}productKey{productKey}timestamp{timestamp}";
        
        byte[] keyBytes = Encoding.UTF8.GetBytes(deviceSecret);
        byte[] contentBytes = Encoding.UTF8.GetBytes(content);
        
        byte[] hashBytes;
        
        switch (signMethod)
        {
            case SignMethodEnum.HmacSha1:
                using (var hmac = new HMACSHA1(keyBytes))
                {
                    hashBytes = hmac.ComputeHash(contentBytes);
                }
                break;
                
            case SignMethodEnum.HmacSha256:
                using (var hmac = new HMACSHA256(keyBytes))
                {
                    hashBytes = hmac.ComputeHash(contentBytes);
                }
                break;
                
            case SignMethodEnum.HmacMd5:
                using (var hmac = new HMACMD5(keyBytes))
                {
                    hashBytes = hmac.ComputeHash(contentBytes);
                }
                break;
                
            default:
                throw new NotSupportedException($"不支持的签名方法：{signMethod}");
        }
        
        return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
    }

    /// <summary>
    /// 从客户端ID中提取时间戳
    /// </summary>
    /// <param name="clientId"></param>
    /// <returns></returns>
    private string? ExtractTimestampFromClientId(string clientId)
    {
        // 阿里云客户端ID格式：{ProductKey}.{DeviceName}|securemode=3,signmethod=hmacsha1,timestamp={timestamp}|
        var match = System.Text.RegularExpressions.Regex.Match(clientId, @"timestamp=([^,|]+)");
        return match.Success ? match.Groups[1].Value : null;
    }

    /// <summary>
    /// 验证JWT令牌
    /// </summary>
    /// <param name="token"></param>
    /// <param name="auth"></param>
    /// <returns></returns>
    private bool ValidateJwtToken(string? token, MqttClientAuth auth)
    {
        // 这里应该实现JWT令牌验证逻辑
        // 为了演示，这里只做简单检查
        return !string.IsNullOrWhiteSpace(token) && !string.IsNullOrWhiteSpace(auth.JwtSecret);
    }

    /// <summary>
    /// 验证证书
    /// </summary>
    /// <param name="certificate"></param>
    /// <param name="auth"></param>
    /// <returns></returns>
    private bool ValidateCertificate(string? certificate, MqttClientAuth auth)
    {
        // 这里应该实现证书验证逻辑
        // 为了演示，这里只做简单检查
        return !string.IsNullOrWhiteSpace(certificate) && !string.IsNullOrWhiteSpace(auth.Certificate);
    }

    /// <summary>
    /// 检查IP访问权限
    /// </summary>
    /// <param name="ipAddress"></param>
    /// <param name="whitelist"></param>
    /// <param name="blacklist"></param>
    /// <returns></returns>
    private bool CheckIpAccess(string? ipAddress, string? whitelist, string? blacklist)
    {
        if (string.IsNullOrWhiteSpace(ipAddress))
            return false;

        // 检查黑名单
        if (!string.IsNullOrWhiteSpace(blacklist))
        {
            var blackIps = ParseIpList(blacklist);
            if (blackIps.Contains(ipAddress))
                return false;
        }

        // 检查白名单
        if (!string.IsNullOrWhiteSpace(whitelist))
        {
            var whiteIps = ParseIpList(whitelist);
            return whiteIps.Contains(ipAddress);
        }

        return true; // 如果没有配置白名单，默认允许
    }

    /// <summary>
    /// 解析IP列表
    /// </summary>
    /// <param name="ipListJson"></param>
    /// <returns></returns>
    private List<string> ParseIpList(string ipListJson)
    {
        try
        {
            return JsonSerializer.Deserialize<List<string>>(ipListJson) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    /// <summary>
    /// 解析主题列表
    /// </summary>
    /// <param name="topicListJson"></param>
    /// <returns></returns>
    private List<string> ParseTopicList(string? topicListJson)
    {
        if (string.IsNullOrWhiteSpace(topicListJson))
            return new List<string>();

        try
        {
            return JsonSerializer.Deserialize<List<string>>(topicListJson) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    /// <summary>
    /// 记录认证成功
    /// </summary>
    /// <param name="auth"></param>
    /// <returns></returns>
    private async Task RecordAuthSuccess(MqttClientAuth auth)
    {
        auth.SuccessCount++;
        auth.FailedCount = 0; // 重置失败次数
        auth.LastAuthTime = DateTime.Now;
        auth.CurrentConnections++;
        
        // 如果之前被锁定，解锁
        if (auth.IsLocked)
        {
            auth.IsLocked = false;
            auth.LockTime = null;
            auth.LockReason = null;
        }

        await _rep.UpdateAsync(auth);
    }

    /// <summary>
    /// 记录认证失败
    /// </summary>
    /// <param name="auth"></param>
    /// <param name="reason"></param>
    /// <returns></returns>
    private async Task RecordAuthFailure(MqttClientAuth auth, string reason)
    {
        auth.FailedCount++;
        
        // 检查是否需要锁定
        if (auth.FailedCount >= auth.MaxFailedCount)
        {
            auth.IsLocked = true;
            auth.LockTime = DateTime.Now;
            auth.LockReason = $"连续失败{auth.FailedCount}次：{reason}";
        }

        await _rep.UpdateAsync(auth);
    }

    #endregion
}

#region 输入输出模型

/// <summary>
/// 分页查询MQTT客户端认证输入
/// </summary>
public class PageMqttClientAuthInput : BasePageInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    public long? InstanceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// 认证模式
    /// </summary>
    public AuthModeEnum? AuthMode { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// AccessKey ID
    /// </summary>
    public string? AccessKeyId { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }

    /// <summary>
    /// 是否锁定
    /// </summary>
    public bool? IsLocked { get; set; }
}

/// <summary>
/// 添加MQTT客户端认证输入
/// </summary>
public class AddMqttClientAuthInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required(ErrorMessage = "客户端ID不能为空")]
    [MaxLength(128, ErrorMessage = "客户端ID长度不能超过128个字符")]
    public string ClientId { get; set; }

    /// <summary>
    /// 认证模式
    /// </summary>
    public AuthModeEnum AuthMode { get; set; } = AuthModeEnum.Username;

    /// <summary>
    /// 用户名
    /// </summary>
    [MaxLength(128, ErrorMessage = "用户名长度不能超过128个字符")]
    public string? Username { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    [MaxLength(256, ErrorMessage = "密码长度不能超过256个字符")]
    public string? Password { get; set; }

    /// <summary>
    /// AccessKey ID
    /// </summary>
    [MaxLength(64, ErrorMessage = "AccessKey ID长度不能超过64个字符")]
    public string? AccessKeyId { get; set; }

    /// <summary>
    /// AccessKey Secret
    /// </summary>
    [MaxLength(256, ErrorMessage = "AccessKey Secret长度不能超过256个字符")]
    public string? AccessKeySecret { get; set; }

    /// <summary>
    /// 签名方法
    /// </summary>
    public SignMethodEnum SignMethod { get; set; } = SignMethodEnum.HmacSha1;

    /// <summary>
    /// JWT密钥
    /// </summary>
    [MaxLength(512, ErrorMessage = "JWT密钥长度不能超过512个字符")]
    public string? JwtSecret { get; set; }

    /// <summary>
    /// JWT过期时间（秒）
    /// </summary>
    public int? JwtExpiry { get; set; }

    /// <summary>
    /// 证书内容
    /// </summary>
    public string? Certificate { get; set; }

    /// <summary>
    /// 私钥内容
    /// </summary>
    public string? PrivateKey { get; set; }

    /// <summary>
    /// CA证书内容
    /// </summary>
    public string? CaCertificate { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpireTime { get; set; }

    /// <summary>
    /// 最大失败次数
    /// </summary>
    [Range(1, 100, ErrorMessage = "最大失败次数范围必须在1-100之间")]
    public int MaxFailedCount { get; set; } = 5;

    /// <summary>
    /// 锁定时长（分钟）
    /// </summary>
    [Range(1, 1440, ErrorMessage = "锁定时长范围必须在1-1440分钟之间")]
    public int LockDuration { get; set; } = 30;

    /// <summary>
    /// IP白名单
    /// </summary>
    public string? IpWhitelist { get; set; }

    /// <summary>
    /// IP黑名单
    /// </summary>
    public string? IpBlacklist { get; set; }

    /// <summary>
    /// 允许的主题列表
    /// </summary>
    public string? AllowedTopics { get; set; }

    /// <summary>
    /// 禁止的主题列表
    /// </summary>
    public string? DeniedTopics { get; set; }

    /// <summary>
    /// 最大连接数
    /// </summary>
    [Range(1, 1000, ErrorMessage = "最大连接数范围必须在1-1000之间")]
    public int MaxConnections { get; set; } = 1;

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    [Range(10, 3600, ErrorMessage = "连接超时时间范围必须在10-3600秒之间")]
    public int ConnectionTimeout { get; set; } = 60;

    /// <summary>
    /// 最大消息大小（字节）
    /// </summary>
    [Range(1024, 10485760, ErrorMessage = "最大消息大小范围必须在1KB-10MB之间")]
    public int MaxMessageSize { get; set; } = 1048576;

    /// <summary>
    /// 最大订阅数量
    /// </summary>
    [Range(1, 10000, ErrorMessage = "最大订阅数量范围必须在1-10000之间")]
    public int MaxSubscriptions { get; set; } = 100;

    /// <summary>
    /// 消息速率限制（消息/秒）
    /// </summary>
    [Range(1, 100000, ErrorMessage = "消息速率限制范围必须在1-100000之间")]
    public int MessageRateLimit { get; set; } = 1000;

    /// <summary>
    /// 字节速率限制（字节/秒）
    /// </summary>
    [Range(1024, 104857600, ErrorMessage = "字节速率限制范围必须在1KB-100MB之间")]
    public long ByteRateLimit { get; set; } = 1048576;

    /// <summary>
    /// 扩展属性
    /// </summary>
    public string? ExtendedProperties { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 更新MQTT客户端认证输入
/// </summary>
public class UpdateMqttClientAuthInput : AddMqttClientAuthInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 删除MQTT客户端认证输入
/// </summary>
public class DeleteMqttClientAuthInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 批量删除MQTT客户端认证输入
/// </summary>
public class BatchDeleteMqttClientAuthInput
{
    /// <summary>
    /// 主键ID集合
    /// </summary>
    [Required(ErrorMessage = "主键ID集合不能为空")]
    public List<long> Ids { get; set; }
}

/// <summary>
/// 切换认证状态输入
/// </summary>
public class ToggleAuthStatusInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}

/// <summary>
/// 解锁认证输入
/// </summary>
public class UnlockAuthInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 验证认证输入
/// </summary>
public class ValidateAuthInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required(ErrorMessage = "客户端ID不能为空")]
    public string ClientId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// 证书
    /// </summary>
    public string? Certificate { get; set; }

    /// <summary>
    /// IP地址
    /// </summary>
    public string? IpAddress { get; set; }
}

/// <summary>
/// 计算签名输入
/// </summary>
public class CalculateSignatureInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 产品Key
    /// </summary>
    [Required(ErrorMessage = "产品Key不能为空")]
    public string ProductKey { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    [Required(ErrorMessage = "设备名称不能为空")]
    public string DeviceName { get; set; }

    /// <summary>
    /// 设备密钥
    /// </summary>
    [Required(ErrorMessage = "设备密钥不能为空")]
    public string DeviceSecret { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required(ErrorMessage = "客户端ID不能为空")]
    public string ClientId { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    [Required(ErrorMessage = "时间戳不能为空")]
    public string Timestamp { get; set; }

    /// <summary>
    /// 签名方法
    /// </summary>
    public SignMethodEnum SignMethod { get; set; } = SignMethodEnum.HmacSha1;
}

/// <summary>
/// MQTT客户端认证分页输出
/// </summary>
public class MqttClientAuthPageOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 实例ID
    /// </summary>
    public long InstanceId { get; set; }

    /// <summary>
    /// 实例名称
    /// </summary>
    public string InstanceName { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 认证模式
    /// </summary>
    public AuthModeEnum AuthMode { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// AccessKey ID
    /// </summary>
    public string? AccessKeyId { get; set; }

    /// <summary>
    /// 签名方法
    /// </summary>
    public SignMethodEnum SignMethod { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpireTime { get; set; }

    /// <summary>
    /// 最后认证时间
    /// </summary>
    public DateTime? LastAuthTime { get; set; }

    /// <summary>
    /// 认证成功次数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 认证失败次数
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 是否锁定
    /// </summary>
    public bool IsLocked { get; set; }

    /// <summary>
    /// 锁定时间
    /// </summary>
    public DateTime? LockTime { get; set; }

    /// <summary>
    /// 锁定原因
    /// </summary>
    public string? LockReason { get; set; }

    /// <summary>
    /// 最大连接数
    /// </summary>
    public int MaxConnections { get; set; }

    /// <summary>
    /// 当前连接数
    /// </summary>
    public int CurrentConnections { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}

/// <summary>
/// 认证验证结果
/// </summary>
public class AuthValidationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = "";

    /// <summary>
    /// 允许的主题列表
    /// </summary>
    public List<string> AllowedTopics { get; set; } = new();

    /// <summary>
    /// 禁止的主题列表
    /// </summary>
    public List<string> DeniedTopics { get; set; } = new();
}

#endregion