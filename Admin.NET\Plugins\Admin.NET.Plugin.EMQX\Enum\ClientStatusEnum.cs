// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.ComponentModel;

namespace Admin.NET.Plugin.EMQX.Enum;

/// <summary>
/// MQTT客户端连接状态枚举
/// </summary>
[Description("MQTT客户端连接状态")]
public enum ClientStatusEnum
{
    /// <summary>
    /// 离线
    /// </summary>
    [Description("离线")]
    Offline = 0,

    /// <summary>
    /// 在线
    /// </summary>
    [Description("在线")]
    Online = 1,

    /// <summary>
    /// 已连接
    /// </summary>
    [Description("已连接")]
    Connected = 2,

    /// <summary>
    /// 连接中
    /// </summary>
    [Description("连接中")]
    Connecting = 3,

    /// <summary>
    /// 断开中
    /// </summary>
    [Description("断开中")]
    Disconnecting = 4,

    /// <summary>
    /// 重连中
    /// </summary>
    [Description("重连中")]
    Reconnecting = 5,

    /// <summary>
    /// 认证失败
    /// </summary>
    [Description("认证失败")]
    AuthFailed = 5,

    /// <summary>
    /// 被踢下线
    /// </summary>
    [Description("被踢下线")]
    Kicked = 6,

    /// <summary>
    /// 异常断开
    /// </summary>
    [Description("异常断开")]
    Abnormal = 7,

    /// <summary>
    /// 禁用
    /// </summary>
    [Description("禁用")]
    Disabled = 8
}