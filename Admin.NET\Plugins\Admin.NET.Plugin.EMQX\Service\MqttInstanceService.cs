// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Plugin.EMQX.Entity;
using Admin.NET.Plugin.EMQX.Enum;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Mapster;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Plugin.EMQX.Service;

/// <summary>
/// MQTT实例服务
/// </summary>
[ApiDescriptionSettings("Plugin-EMQX", Name = "MqttInstance", Order = 100)]
public class MqttInstanceService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MqttInstance> _rep;
    private readonly ILogger<MqttInstanceService> _logger;

    public MqttInstanceService(SqlSugarRepository<MqttInstance> rep, ILogger<MqttInstanceService> logger)
    {
        _rep = rep;
        _logger = logger;
    }

    /// <summary>
    /// 分页查询MQTT实例
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<MqttInstance>> GetPageAsync(PageMqttInstanceInput input)
    {
        return await _rep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.InstanceName), x => x.InstanceName.Contains(input.InstanceName!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InstanceCode), x => x.InstanceCode.Contains(input.InstanceCode!))
            .WhereIF(input.InstanceType.HasValue, x => x.InstanceType == input.InstanceType)
            .WhereIF(input.Status.HasValue, x => x.Status == input.Status)
            .WhereIF(input.IsEnabled.HasValue, x => x.IsEnabled == input.IsEnabled)
            .OrderBy(x => x.CreateTime, OrderByType.Desc)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取MQTT实例详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<MqttInstance> GetDetailAsync([Required] long id)
    {
        var instance = await _rep.GetByIdAsync(id);
        if (instance == null)
            throw Oops.Oh("实例不存在");
        return instance;
    }

    /// <summary>
    /// 添加MQTT实例
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task<long> AddAsync(AddMqttInstanceInput input)
    {
        // 检查实例编码是否重复
        var existInstance = await _rep.GetFirstAsync(x => x.InstanceCode == input.InstanceCode);
        if (existInstance != null)
            throw Oops.Oh("实例编码已存在");

        var instance = input.Adapt<MqttInstance>();
        instance.Status = InstanceStatusEnum.Stopped;
        instance.ConnectionCount = 0;
        instance.MessageCount = 0;
        instance.ByteCount = 0;
        instance.LastHeartbeatTime = DateTime.Now;

        var newInstance = await _rep.InsertReturnEntityAsync(instance);
        return newInstance.Id;
    }

    /// <summary>
    /// 更新MQTT实例
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task UpdateAsync(UpdateMqttInstanceInput input)
    {
        var instance = await _rep.GetByIdAsync(input.Id);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        // 检查实例编码是否重复（排除自己）
        var existInstance = await _rep.GetFirstAsync(x => x.InstanceCode == input.InstanceCode && x.Id != input.Id);
        if (existInstance != null)
            throw Oops.Oh("实例编码已存在");

        var updateInstance = input.Adapt<MqttInstance>();
        await _rep.UpdateAsync(updateInstance);
    }

    /// <summary>
    /// 删除MQTT实例
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task DeleteAsync(DeleteMqttInstanceInput input)
    {
        var instance = await _rep.GetByIdAsync(input.Id);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        // 检查是否有关联的客户端
        var clientCount = await _rep.Context.Queryable<MqttClient>()
            .Where(x => x.InstanceId == input.Id && !x.IsDelete)
            .CountAsync();
        if (clientCount > 0)
            throw Oops.Oh($"实例下还有 {clientCount} 个客户端，无法删除");

        await _rep.DeleteAsync(instance);
    }

    /// <summary>
    /// 批量删除MQTT实例
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "BatchDelete")]
    public async Task BatchDeleteAsync(BatchDeleteMqttInstanceInput input)
    {
        if (input.Ids == null || !input.Ids.Any())
            throw Oops.Oh("请选择要删除的实例");

        foreach (var id in input.Ids)
        {
            var instance = await _rep.GetByIdAsync(id);
            if (instance == null) continue;

            // 检查是否有关联的客户端
            var clientCount = await _rep.Context.Queryable<MqttClient>()
                .Where(x => x.InstanceId == id && !x.IsDelete)
                .CountAsync();
            if (clientCount > 0)
                throw Oops.Oh($"实例 {instance.InstanceName} 下还有 {clientCount} 个客户端，无法删除");
        }

        await _rep.DeleteByIdsAsync(input.Ids.Cast<dynamic>().ToArray());
    }

    /// <summary>
    /// 启用/禁用MQTT实例
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "ToggleStatus")]
    public async Task ToggleStatusAsync(ToggleStatusInput input)
    {
        var instance = await _rep.GetByIdAsync(input.Id);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        instance.IsEnabled = input.IsEnabled;
        if (!input.IsEnabled)
        {
            instance.Status = InstanceStatusEnum.Stopped;
        }

        await _rep.UpdateAsync(instance);
    }

    /// <summary>
    /// 更新实例状态
    /// </summary>
    /// <param name="instanceId"></param>
    /// <param name="status"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "UpdateStatus")]
    public async Task UpdateStatusAsync([Required] long instanceId, [Required] InstanceStatusEnum status)
    {
        var instance = await _rep.GetByIdAsync(instanceId);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        instance.Status = status;
        instance.LastHeartbeatTime = DateTime.Now;

        await _rep.UpdateAsync(instance);
    }

    /// <summary>
    /// 更新实例统计信息
    /// </summary>
    /// <param name="instanceId"></param>
    /// <param name="connectionCount"></param>
    /// <param name="messageCount"></param>
    /// <param name="byteCount"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "UpdateStatistics")]
    public async Task UpdateStatisticsAsync([Required] long instanceId, int connectionCount, long messageCount, long byteCount)
    {
        var instance = await _rep.GetByIdAsync(instanceId);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        instance.ConnectionCount = connectionCount;
        instance.MessageCount = messageCount;
        instance.ByteCount = byteCount;
        instance.LastHeartbeatTime = DateTime.Now;

        await _rep.UpdateAsync(instance);
    }

    /// <summary>
    /// 获取实例列表（下拉选择）
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<MqttInstanceSelectOutput>> GetListAsync()
    {
        var instances = await _rep.AsQueryable()
            .Where(x => x.IsEnabled)
            .OrderBy(x => x.InstanceName)
            .Select(x => new MqttInstanceSelectOutput
            {
                Id = x.Id,
                InstanceName = x.InstanceName,
                InstanceCode = x.InstanceCode,
                InstanceType = x.InstanceType,
                Status = x.Status
            })
            .ToListAsync();

        return instances;
    }

    /// <summary>
    /// 生成阿里云兼容的设备ID
    /// </summary>
    /// <param name="instanceId"></param>
    /// <param name="deviceName"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GenerateAliyunDeviceId")]
    public async Task<string> GenerateAliyunDeviceIdAsync([Required] long instanceId, [Required] string deviceName)
    {
        var instance = await _rep.GetByIdAsync(instanceId);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        if (string.IsNullOrWhiteSpace(instance.AliyunProductKey))
            throw Oops.Oh("实例未配置阿里云产品Key");

        // 阿里云设备ID格式：{ProductKey}.{DeviceName}
        return $"{instance.AliyunProductKey}.{deviceName}";
    }

    /// <summary>
    /// 生成设备组ID
    /// </summary>
    /// <param name="instanceId"></param>
    /// <param name="groupName"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GenerateGroupId")]
    public async Task<string> GenerateGroupIdAsync([Required] long instanceId, [Required] string groupName)
    {
        var instance = await _rep.GetByIdAsync(instanceId);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        var prefix = !string.IsNullOrWhiteSpace(instance.GroupIdPrefix) ? instance.GroupIdPrefix : "group";
        return $"{prefix}_{groupName}_{instanceId}";
    }
}

#region 输入输出模型

/// <summary>
/// 分页查询MQTT实例输入
/// </summary>
public class PageMqttInstanceInput : BasePageInput
{
    /// <summary>
    /// 实例名称
    /// </summary>
    public string? InstanceName { get; set; }

    /// <summary>
    /// 实例编码
    /// </summary>
    public string? InstanceCode { get; set; }

    /// <summary>
    /// 实例类型
    /// </summary>
    public InstanceTypeEnum? InstanceType { get; set; }

    /// <summary>
    /// 实例状态
    /// </summary>
    public InstanceStatusEnum? Status { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }
}

/// <summary>
/// 添加MQTT实例输入
/// </summary>
public class AddMqttInstanceInput
{
    /// <summary>
    /// 实例名称
    /// </summary>
    [Required(ErrorMessage = "实例名称不能为空")]
    [MaxLength(100, ErrorMessage = "实例名称长度不能超过100个字符")]
    public string InstanceName { get; set; }

    /// <summary>
    /// 实例编码
    /// </summary>
    [Required(ErrorMessage = "实例编码不能为空")]
    [MaxLength(50, ErrorMessage = "实例编码长度不能超过50个字符")]
    public string InstanceCode { get; set; }

    /// <summary>
    /// 实例类型
    /// </summary>
    public InstanceTypeEnum InstanceType { get; set; } = InstanceTypeEnum.Standard;

    /// <summary>
    /// EMQX服务器地址
    /// </summary>
    [Required(ErrorMessage = "服务器地址不能为空")]
    [MaxLength(255, ErrorMessage = "服务器地址长度不能超过255个字符")]
    public string ServerHost { get; set; }

    /// <summary>
    /// EMQX服务器端口
    /// </summary>
    [Range(1, 65535, ErrorMessage = "端口范围必须在1-65535之间")]
    public int ServerPort { get; set; } = 1883;

    /// <summary>
    /// EMQX管理API地址
    /// </summary>
    [MaxLength(255, ErrorMessage = "API地址长度不能超过255个字符")]
    public string? ApiUrl { get; set; }

    /// <summary>
    /// EMQX管理API用户名
    /// </summary>
    [MaxLength(100, ErrorMessage = "API用户名长度不能超过100个字符")]
    public string? ApiUsername { get; set; }

    /// <summary>
    /// EMQX管理API密码
    /// </summary>
    [MaxLength(100, ErrorMessage = "API密码长度不能超过100个字符")]
    public string? ApiPassword { get; set; }

    /// <summary>
    /// 是否启用SSL
    /// </summary>
    public bool EnableSsl { get; set; } = false;

    /// <summary>
    /// SSL端口
    /// </summary>
    public int? SslPort { get; set; }

    /// <summary>
    /// 是否启用WebSocket
    /// </summary>
    public bool EnableWebSocket { get; set; } = false;

    /// <summary>
    /// WebSocket端口
    /// </summary>
    public int? WebSocketPort { get; set; }

    /// <summary>
    /// 阿里云产品Key
    /// </summary>
    [MaxLength(50, ErrorMessage = "产品Key长度不能超过50个字符")]
    public string? AliyunProductKey { get; set; }

    /// <summary>
    /// 阿里云区域ID
    /// </summary>
    [MaxLength(50, ErrorMessage = "区域ID长度不能超过50个字符")]
    public string? AliyunRegionId { get; set; }

    /// <summary>
    /// 阿里云实例ID
    /// </summary>
    [MaxLength(100, ErrorMessage = "实例ID长度不能超过100个字符")]
    public string? AliyunInstanceId { get; set; }

    /// <summary>
    /// 设备ID前缀
    /// </summary>
    [MaxLength(50, ErrorMessage = "设备ID前缀长度不能超过50个字符")]
    public string? DeviceIdPrefix { get; set; }

    /// <summary>
    /// 组ID前缀
    /// </summary>
    [MaxLength(50, ErrorMessage = "组ID前缀长度不能超过50个字符")]
    public string? GroupIdPrefix { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 配置信息
    /// </summary>
    public string? ConfigJson { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 更新MQTT实例输入
/// </summary>
public class UpdateMqttInstanceInput : AddMqttInstanceInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 删除MQTT实例输入
/// </summary>
public class DeleteMqttInstanceInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 批量删除MQTT实例输入
/// </summary>
public class BatchDeleteMqttInstanceInput
{
    /// <summary>
    /// 主键ID集合
    /// </summary>
    [Required(ErrorMessage = "主键ID集合不能为空")]
    public List<long> Ids { get; set; }
}

/// <summary>
/// 切换状态输入
/// </summary>
public class ToggleStatusInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}

/// <summary>
/// MQTT实例选择输出
/// </summary>
public class MqttInstanceSelectOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 实例名称
    /// </summary>
    public string InstanceName { get; set; }

    /// <summary>
    /// 实例编码
    /// </summary>
    public string InstanceCode { get; set; }

    /// <summary>
    /// 实例类型
    /// </summary>
    public InstanceTypeEnum InstanceType { get; set; }

    /// <summary>
    /// 实例状态
    /// </summary>
    public InstanceStatusEnum Status { get; set; }
}

#endregion