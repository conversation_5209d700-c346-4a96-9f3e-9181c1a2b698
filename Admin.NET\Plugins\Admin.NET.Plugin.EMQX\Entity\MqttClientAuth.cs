// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Plugin.EMQX.Enum;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Plugin.EMQX.Entity;

/// <summary>
/// MQTT客户端认证表
/// </summary>
[SugarTable("mqtt_client_auth", "MQTT客户端认证表")]
[SugarIndex("idx_mqtt_client_auth_client_id", nameof(ClientId), OrderByType.Asc)]
[SugarIndex("idx_mqtt_client_auth_instance", nameof(InstanceId), OrderByType.Asc)]
[SugarIndex("idx_mqtt_client_auth_access_key", nameof(AccessKeyId), OrderByType.Asc)]
[SugarIndex("idx_mqtt_client_auth_enabled", nameof(IsEnabled), OrderByType.Asc)]
[SugarIndex("uk_mqtt_client_auth_client_instance", $"{nameof(ClientId)},{nameof(InstanceId)}", OrderByType.Asc, true)]
public class MqttClientAuth : EntityBaseDel
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [SugarColumn(ColumnDescription = "实例ID")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 客户端ID（字符串格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "客户端ID字符串", Length = 128)]
    [Required, MaxLength(128)]
    public string ClientId { get; set; }

    /// <summary>
    /// 客户端表引用ID（外键）
    /// </summary>
    [SugarColumn(ColumnDescription = "客户端表引用ID", IsNullable = true)]
    public long? ClientRefId { get; set; }

    /// <summary>
    /// 认证模式
    /// </summary>
    [SugarColumn(ColumnDescription = "认证模式")]
    public AuthModeEnum AuthMode { get; set; } = AuthModeEnum.Username;

    /// <summary>
    /// 用户名（用户名密码认证）
    /// </summary>
    [SugarColumn(ColumnDescription = "用户名", Length = 128, IsNullable = true)]
    [MaxLength(128)]
    public string? Username { get; set; }

    /// <summary>
    /// 密码（用户名密码认证，加密存储）
    /// </summary>
    [SugarColumn(ColumnDescription = "密码", Length = 256, IsNullable = true)]
    [MaxLength(256)]
    public string? Password { get; set; }

    /// <summary>
    /// 密码哈希值（兼容数据库字段）
    /// </summary>
    [SugarColumn(ColumnDescription = "密码哈希值", Length = 255, IsNullable = true, ColumnName = "PasswordHash")]
    [MaxLength(255)]
    public string? PasswordHash { get; set; }

    /// <summary>
    /// 密码盐值
    /// </summary>
    [SugarColumn(ColumnDescription = "密码盐值", Length = 64, IsNullable = true, ColumnName = "Salt")]
    [MaxLength(64)]
    public string? PasswordSalt { get; set; }

    /// <summary>
    /// AccessKey ID（阿里云签名认证）
    /// </summary>
    [SugarColumn(ColumnDescription = "AccessKey ID", Length = 64, IsNullable = true, ColumnName = "AccessKey")]
    [MaxLength(64)]
    public string? AccessKeyId { get; set; }

    /// <summary>
    /// AccessKey Secret（阿里云签名认证，加密存储）
    /// </summary>
    [SugarColumn(ColumnDescription = "AccessKey Secret", Length = 256, IsNullable = true, ColumnName = "SecretKey")]
    [MaxLength(256)]
    public string? AccessKeySecret { get; set; }

    /// <summary>
    /// 签名方法
    /// </summary>
    [SugarColumn(ColumnDescription = "签名方法")]
    public SignMethodEnum SignMethod { get; set; } = SignMethodEnum.HmacSha1;

    /// <summary>
    /// JWT密钥（JWT认证）
    /// </summary>
    [SugarColumn(ColumnDescription = "JWT密钥", Length = 512, IsNullable = true)]
    [MaxLength(512)]
    public string? JwtSecret { get; set; }

    /// <summary>
    /// JWT过期时间（秒）
    /// </summary>
    [SugarColumn(ColumnDescription = "JWT过期时间", IsNullable = true)]
    public int? JwtExpiry { get; set; }

    /// <summary>
    /// JWT令牌
    /// </summary>
    [SugarColumn(ColumnDescription = "JWT令牌", ColumnDataType = "text", IsNullable = true, ColumnName = "JwtToken")]
    public string? JwtToken { get; set; }

    /// <summary>
    /// 证书内容（证书认证）
    /// </summary>
    [SugarColumn(ColumnDescription = "证书内容", ColumnDataType = "text", IsNullable = true, ColumnName = "CertificateData")]
    public string? Certificate { get; set; }

    /// <summary>
    /// 私钥内容（证书认证）
    /// </summary>
    [SugarColumn(ColumnDescription = "私钥内容", ColumnDataType = "text", IsNullable = true)]
    public string? PrivateKey { get; set; }

    /// <summary>
    /// CA证书内容
    /// </summary>
    [SugarColumn(ColumnDescription = "CA证书内容", ColumnDataType = "text", IsNullable = true)]
    public string? CaCertificate { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 允许的IP地址列表
    /// </summary>
    [SugarColumn(ColumnDescription = "允许的IP地址列表", ColumnDataType = "text", IsNullable = true, ColumnName = "AllowedIPs")]
    public string? AllowedIPs { get; set; }

    /// <summary>
    /// 拒绝的IP地址列表
    /// </summary>
    [SugarColumn(ColumnDescription = "拒绝的IP地址列表", ColumnDataType = "text", IsNullable = true, ColumnName = "DeniedIPs")]
    public string? DeniedIPs { get; set; }

    /// <summary>
    /// 速率限制
    /// </summary>
    [SugarColumn(ColumnDescription = "速率限制", IsNullable = true, ColumnName = "RateLimit")]
    public int? RateLimit { get; set; }

    /// <summary>
    /// 登录尝试次数
    /// </summary>
    [SugarColumn(ColumnDescription = "登录尝试次数", IsNullable = true, ColumnName = "LoginAttempts")]
    public int? LoginAttempts { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    [SugarColumn(ColumnDescription = "过期时间", IsNullable = true)]
    public DateTime? ExpireTime { get; set; }

    /// <summary>
    /// 最后认证时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后认证时间", IsNullable = true)]
    public DateTime? LastAuthTime { get; set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后登录时间", IsNullable = true, ColumnName = "LastLoginTime")]
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 最后登录IP
    /// </summary>
    [SugarColumn(ColumnDescription = "最后登录IP", Length = 45, IsNullable = true, ColumnName = "LastLoginIP")]
    public string? LastLoginIP { get; set; }

    /// <summary>
    /// 认证成功次数
    /// </summary>
    [SugarColumn(ColumnDescription = "认证成功次数")]
    public int SuccessCount { get; set; } = 0;

    /// <summary>
    /// 认证成功总数
    /// </summary>
    [SugarColumn(ColumnDescription = "认证成功总数", IsNullable = true, ColumnName = "AuthSuccessCount")]
    public long? AuthSuccessCount { get; set; }

    /// <summary>
    /// 认证失败次数
    /// </summary>
    [SugarColumn(ColumnDescription = "认证失败次数")]
    public int FailedCount { get; set; } = 0;

    /// <summary>
    /// 是否锁定
    /// </summary>
    [SugarColumn(ColumnDescription = "是否锁定")]
    public bool IsLocked { get; set; } = false;

    /// <summary>
    /// 锁定时间
    /// </summary>
    [SugarColumn(ColumnDescription = "锁定时间", IsNullable = true)]
    public DateTime? LockTime { get; set; }

    /// <summary>
    /// 解锁时间
    /// </summary>
    [SugarColumn(ColumnDescription = "解锁时间", IsNullable = true, ColumnName = "UnlockTime")]
    public DateTime? UnlockTime { get; set; }

    /// <summary>
    /// 锁定原因
    /// </summary>
    [SugarColumn(ColumnDescription = "锁定原因", Length = 256, IsNullable = true)]
    [MaxLength(256)]
    public string? LockReason { get; set; }

    /// <summary>
    /// 最大失败次数
    /// </summary>
    [SugarColumn(ColumnDescription = "最大失败次数")]
    public int MaxFailedCount { get; set; } = 5;

    /// <summary>
    /// 锁定时长（分钟）
    /// </summary>
    [SugarColumn(ColumnDescription = "锁定时长")]
    public int LockDuration { get; set; } = 30;

    /// <summary>
    /// IP白名单（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "IP白名单", ColumnDataType = "text", IsNullable = true)]
    public string? IpWhitelist { get; set; }

    /// <summary>
    /// IP黑名单（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "IP黑名单", ColumnDataType = "text", IsNullable = true)]
    public string? IpBlacklist { get; set; }

    /// <summary>
    /// 允许的主题列表（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "允许的主题列表", ColumnDataType = "text", IsNullable = true)]
    public string? AllowedTopics { get; set; }

    /// <summary>
    /// 禁止的主题列表（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "禁止的主题列表", ColumnDataType = "text", IsNullable = true)]
    public string? DeniedTopics { get; set; }

    /// <summary>
    /// 最大连接数
    /// </summary>
    [SugarColumn(ColumnDescription = "最大连接数")]
    public int MaxConnections { get; set; } = 1;

    /// <summary>
    /// 当前连接数
    /// </summary>
    [SugarColumn(ColumnDescription = "当前连接数")]
    public int CurrentConnections { get; set; } = 0;

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    [SugarColumn(ColumnDescription = "连接超时时间")]
    public int ConnectionTimeout { get; set; } = 60;

    /// <summary>
    /// 最大消息大小（字节）
    /// </summary>
    [SugarColumn(ColumnDescription = "最大消息大小")]
    public int MaxMessageSize { get; set; } = 1048576; // 1MB

    /// <summary>
    /// 最大订阅数量
    /// </summary>
    [SugarColumn(ColumnDescription = "最大订阅数量")]
    public int MaxSubscriptions { get; set; } = 100;

    /// <summary>
    /// 消息速率限制（消息/秒）
    /// </summary>
    [SugarColumn(ColumnDescription = "消息速率限制")]
    public int MessageRateLimit { get; set; } = 1000;

    /// <summary>
    /// 字节速率限制（字节/秒）
    /// </summary>
    [SugarColumn(ColumnDescription = "字节速率限制")]
    public long ByteRateLimit { get; set; } = 1048576; // 1MB/s

    /// <summary>
    /// 扩展属性（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展属性", ColumnDataType = "text", IsNullable = true)]
    public string? ExtendedProperties { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string? Remark { get; set; }

    /// <summary>
    /// 所属实例
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(InstanceId))]
    public MqttInstance? Instance { get; set; }

    /// <summary>
    /// 客户端信息
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MqttClient.ClientId), nameof(ClientId))]
    public MqttClient? Client { get; set; }
}