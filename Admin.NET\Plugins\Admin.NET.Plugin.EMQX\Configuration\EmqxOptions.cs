// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Furion.ConfigurableOptions;

namespace Admin.NET.Plugin.EMQX.Configuration;

/// <summary>
/// EMQX插件配置选项
/// </summary>
[OptionsSettings("EMQX")]
public sealed class EmqxOptions : IConfigurableOptions
{
    /// <summary>
    /// 默认EMQX实例配置
    /// </summary>
    public DefaultInstanceOptions DefaultInstance { get; set; } = new();

    /// <summary>
    /// HTTP客户端配置
    /// </summary>
    public HttpClientOptions HttpClient { get; set; } = new();

    /// <summary>
    /// 缓存配置
    /// </summary>
    public CacheOptions Cache { get; set; } = new();

    /// <summary>
    /// 监控配置
    /// </summary>
    public MonitoringOptions Monitoring { get; set; } = new();
}

/// <summary>
/// 默认实例配置
/// </summary>
public class DefaultInstanceOptions
{
    /// <summary>
    /// 服务器地址
    /// </summary>
    public string ServerHost { get; set; } = "localhost";

    /// <summary>
    /// 服务器端口
    /// </summary>
    public int ServerPort { get; set; } = 1883;

    /// <summary>
    /// API地址
    /// </summary>
    public string ApiHost { get; set; } = "localhost";

    /// <summary>
    /// API端口
    /// </summary>
    public int ApiPort { get; set; } = 18083;

    /// <summary>
    /// API用户名
    /// </summary>
    public string ApiUsername { get; set; } = "admin";

    /// <summary>
    /// API密码
    /// </summary>
    public string ApiPassword { get; set; } = "public";

    /// <summary>
    /// 是否启用SSL
    /// </summary>
    public bool EnableSsl { get; set; } = false;

    /// <summary>
    /// SSL端口
    /// </summary>
    public int SslPort { get; set; } = 8883;
}

/// <summary>
/// HTTP客户端配置
/// </summary>
public class HttpClientOptions
{
    /// <summary>
    /// 超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 重试间隔（毫秒）
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;
}

/// <summary>
/// 缓存配置
/// </summary>
public class CacheOptions
{
    /// <summary>
    /// 实例信息缓存时间（分钟）
    /// </summary>
    public int InstanceCacheMinutes { get; set; } = 30;

    /// <summary>
    /// 客户端状态缓存时间（分钟）
    /// </summary>
    public int ClientStatusCacheMinutes { get; set; } = 5;

    /// <summary>
    /// 主题信息缓存时间（分钟）
    /// </summary>
    public int TopicCacheMinutes { get; set; } = 15;
}

/// <summary>
/// 监控配置
/// </summary>
public class MonitoringOptions
{
    /// <summary>
    /// 是否启用监控
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 监控间隔（秒）
    /// </summary>
    public int IntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 是否记录详细日志
    /// </summary>
    public bool DetailedLogging { get; set; } = false;

    /// <summary>
    /// 性能指标收集
    /// </summary>
    public bool CollectMetrics { get; set; } = true;
}