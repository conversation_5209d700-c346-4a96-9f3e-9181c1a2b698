// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Plugin.EMQX.Configuration;
using Admin.NET.Plugin.EMQX.Service;
using Furion;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// EMQX多实例管理插件启动类
/// </summary>
[AppStartup(100)]
public class Startup : AppStartup
{
    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        // 注册EMQX插件配置选项
        services.AddConfigurableOptions<EmqxOptions>();

        // 注册EMQX插件服务
        // 这些服务已经通过ITransient接口自动注册，这里可以添加额外的配置
        services.AddScoped<MqttInstanceService>();
        services.AddScoped<MqttClientService>();
        services.AddScoped<MqttTopicService>();
        services.AddScoped<MqttSubscriptionService>();
        services.AddScoped<MqttClientAuthService>();

        // 注册HTTP客户端用于EMQX API调用
        services.AddHttpClient("EMQX", client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
            client.DefaultRequestHeaders.Add("User-Agent", "Admin.NET-EMQX-Plugin/1.0");
        });

        // 可以在这里添加其他配置，如缓存、消息队列等
    }

    /// <summary>
    /// 配置应用程序
    /// </summary>
    /// <param name="app"></param>
    /// <param name="env"></param>
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        // 可以在这里添加中间件配置
        // 例如：EMQX相关的中间件、路由配置等
    }
}