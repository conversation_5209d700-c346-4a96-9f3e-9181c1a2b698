{"$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json", "EMQX": {"DefaultInstance": {"ServerHost": "localhost", "ServerPort": 1883, "ApiHost": "localhost", "ApiPort": 18083, "ApiUsername": "admin", "ApiPassword": "public", "EnableSsl": false, "SslPort": 8883}, "HttpClient": {"TimeoutSeconds": 30, "RetryCount": 3, "RetryDelayMs": 1000}, "Cache": {"InstanceCacheMinutes": 30, "ClientStatusCacheMinutes": 5, "TopicCacheMinutes": 15}, "Monitoring": {"Enabled": true, "IntervalSeconds": 60, "DetailedLogging": false, "CollectMetrics": true}}}