<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <NoWarn>1701;1702;1591;8632</NoWarn>
    <DocumentationFile></DocumentationFile>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <Nullable>disable</Nullable>
    <Copyright>Admin.NET</Copyright>
    <Description>Admin.NET EMQX多实例管理插件</Description>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Admin.NET.Core\Admin.NET.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

</Project>