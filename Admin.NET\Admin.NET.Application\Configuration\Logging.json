{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information",
      "AspNetCoreRateLimit": "None",
      "System.Net.Http.HttpClient": "Warning"
    },
    "File": {
      "Enabled": true, // 启用文件日志
      "FileName": "logs/{0:yyyyMMdd}_{1}.log", // 日志文件
      "Append": true, // 追加覆盖
      "MinimumLevel": "Error", // 日志级别
      "FileSizeLimitBytes": 10485760, // 10M=10*1024*1024
      "MaxRollingFiles": 30 // 只保留30个文件
    },
    "Database": {
      "Enabled": true, // 启用数据库日志
      "MinimumLevel": "Information" // 日志级别
    },
    "ElasticSearch": {
      "Enabled": false, // 启用ES日志
      "AuthType": "Basic", // ES认证类型，可选 Basic、ApiKey、Base64ApiKey
      "User": "admin", // Basic认证的用户名，使用Basic认证类型时必填
      "Password": "123456", // Basic认证的密码，使用Basic认证类型时必填
      "ApiId": "", // 使用ApiKey认证类型时必填
      "ApiKey": "", // 使用ApiKey认证类型时必填
      "Base64ApiKey": "TmtrOEszNEJuQ0NyaWlydGtROFk6SG1RZ0w3YzBTc2lCanJTYlV3aXNzZw==", // 使用Base64ApiKey认证类型时必填
      "Fingerprint": "37:08:6A:C6:06:CC:9A:43:CF:ED:25:A2:1C:A4:69:57:90:31:2C:06:CA:61:56:39:6A:9C:46:11:BD:22:51:DA", // ES使用Https时的证书指纹
      "ServerUris": [ "http://192.168.1.100:9200" ], // 地址
      "DefaultIndex": "adminnet" // 索引
    },
    "Monitor": {
      "GlobalEnabled": true, // 启用全局拦截日志（建议生产环境关闭，否则对性能有影响）
      "IncludeOfMethods": [], // 拦截特定方法，当GlobalEnabled=false有效
      "ExcludeOfMethods": [], // 排除特定方法，当GlobalEnabled=true有效
      "BahLogLevel": "Information", // Oops.Oh 和 Oops.Bah 业务日志输出级别
      "WithReturnValue": true, // 是否包含返回值，默认true
      "ReturnValueThreshold": 0, // 返回值字符串阈值，默认0全量输出
      "JsonBehavior": "None", // 是否输出Json，默认None(OnlyJson、All)
      "JsonIndented": false, // 是否格式化Json
      "UseUtcTimestamp": false, // 时间格式UTC、LOCAL
      "ConsoleLog": true // 是否显示控制台日志
    }
  }
}