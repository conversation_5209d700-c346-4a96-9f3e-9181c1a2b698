// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Plugin.EMQX.Entity;
using Admin.NET.Plugin.EMQX.Enum;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Mapster;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace Admin.NET.Plugin.EMQX.Service;

/// <summary>
/// MQTT订阅服务
/// </summary>
[ApiDescriptionSettings("Plugin-EMQX", Name = "MqttSubscription", Order = 500)]
public class MqttSubscriptionService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MqttSubscription> _rep;
    private readonly SqlSugarRepository<MqttInstance> _instanceRep;
    private readonly SqlSugarRepository<MqttClient> _clientRep;
    private readonly SqlSugarRepository<MqttTopic> _topicRep;
    private readonly ILogger<MqttSubscriptionService> _logger;

    public MqttSubscriptionService(
        SqlSugarRepository<MqttSubscription> rep,
        SqlSugarRepository<MqttInstance> instanceRep,
        SqlSugarRepository<MqttClient> clientRep,
        SqlSugarRepository<MqttTopic> topicRep,
        ILogger<MqttSubscriptionService> logger)
    {
        _rep = rep;
        _instanceRep = instanceRep;
        _clientRep = clientRep;
        _topicRep = topicRep;
        _logger = logger;
    }

    /// <summary>
    /// 分页查询MQTT订阅
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<MqttSubscriptionPageOutput>> GetPageAsync(PageMqttSubscriptionInput input)
    {
        var query = _rep.AsQueryable()
            .LeftJoin<MqttInstance>((s, i) => s.InstanceId == i.Id)
            .LeftJoin<MqttClient>((s, i, c) => s.ClientId == c.ClientId && s.InstanceId == c.InstanceId)
            .LeftJoin<MqttTopic>((s, i, c, t) => s.TopicName == t.TopicName && s.InstanceId == t.InstanceId)
            .WhereIF(input.InstanceId.HasValue, (s, i, c, t) => s.InstanceId == input.InstanceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.ClientId), (s, i, c, t) => s.ClientId.Contains(input.ClientId!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TopicName), (s, i, c, t) => s.TopicName.Contains(input.TopicName!))
            .WhereIF(input.QosLevel.HasValue, (s, i, c, t) => s.QosLevel == input.QosLevel)
            .WhereIF(input.Status.HasValue, (s, i, c, t) => s.Status == input.Status)
            .WhereIF(input.IsSharedSubscription.HasValue, (s, i, c, t) => s.IsSharedSubscription == input.IsSharedSubscription)
            .WhereIF(input.IsEnabled.HasValue, (s, i, c, t) => s.IsEnabled == input.IsEnabled)
            .Select((s, i, c, t) => new MqttSubscriptionPageOutput
            {
                Id = s.Id,
                InstanceId = s.InstanceId,
                InstanceName = i.InstanceName,
                ClientId = s.ClientId,
                DeviceName = c.DeviceName,
                TopicName = s.TopicName,
                TopicType = t.TopicType,
                QosLevel = s.QosLevel,
                Status = s.Status,
                SubscribeTime = s.SubscribeTime,
                LastActivityTime = s.LastActivityTime,
                MessageReceived = s.MessageReceived,
                MessageDropped = s.MessageDropped,
                BytesReceived = s.BytesReceived,
                IsSharedSubscription = s.IsSharedSubscription,
                SharedGroupName = s.SharedGroupName,
                IsEnabled = s.IsEnabled,
                Priority = s.Priority,
                CreateTime = s.CreateTime
            })
            .OrderBy(x => x.CreateTime, OrderByType.Desc);

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取MQTT订阅详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<MqttSubscription> GetDetailAsync([Required] long id)
    {
        var subscription = await _rep.GetByIdAsync(id);
        if (subscription == null)
            throw Oops.Oh("订阅不存在");
        return subscription;
    }

    /// <summary>
    /// 添加MQTT订阅
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task<long> AddAsync(AddMqttSubscriptionInput input)
    {
        // 验证实例是否存在
        var instance = await _instanceRep.GetByIdAsync(input.InstanceId);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        // 验证客户端是否存在
        var client = await _clientRep.GetFirstAsync(x => x.ClientId == input.ClientId && x.InstanceId == input.InstanceId);
        if (client == null)
            throw Oops.Oh("客户端不存在");

        // 检查订阅是否已存在
        var existSubscription = await _rep.GetFirstAsync(x => 
            x.ClientId == input.ClientId && 
            x.TopicName == input.TopicName && 
            x.InstanceId == input.InstanceId);
        if (existSubscription != null)
            throw Oops.Oh("该订阅已存在");

        var subscription = input.Adapt<MqttSubscription>();
        
        // 初始化统计数据
        subscription.Status = SubscriptionStatusEnum.Subscribed;
        subscription.SubscribeTime = DateTime.Now;
        subscription.MessageReceived = 0;
        subscription.MessageDropped = 0;
        subscription.BytesReceived = 0;
        subscription.CurrentQueueLength = 0;
        subscription.RetryCount = 0;

        // 处理共享订阅
        if (input.TopicName.StartsWith("$share/"))
        {
            subscription.IsSharedSubscription = true;
            var parts = input.TopicName.Split('/');
            if (parts.Length >= 3)
            {
                subscription.SharedGroupName = parts[1];
            }
        }

        var newSubscription = await _rep.InsertReturnEntityAsync(subscription);
        
        // 更新主题订阅者数量
        await UpdateTopicSubscriberCount(input.InstanceId, input.TopicName, 1);
        
        // 更新客户端订阅数量
        await UpdateClientSubscriptionCount(input.InstanceId, input.ClientId, 1);

        return newSubscription.Id;
    }

    /// <summary>
    /// 更新MQTT订阅
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task UpdateAsync(UpdateMqttSubscriptionInput input)
    {
        var subscription = await _rep.GetByIdAsync(input.Id);
        if (subscription == null)
            throw Oops.Oh("订阅不存在");

        var updateSubscription = input.Adapt<MqttSubscription>();
        
        // 保留统计数据
        updateSubscription.Status = subscription.Status;
        updateSubscription.SubscribeTime = subscription.SubscribeTime;
        updateSubscription.UnsubscribeTime = subscription.UnsubscribeTime;
        updateSubscription.MessageReceived = subscription.MessageReceived;
        updateSubscription.MessageDropped = subscription.MessageDropped;
        updateSubscription.BytesReceived = subscription.BytesReceived;
        updateSubscription.LastMessageTime = subscription.LastMessageTime;
        updateSubscription.LastMessageId = subscription.LastMessageId;
        updateSubscription.CurrentQueueLength = subscription.CurrentQueueLength;
        updateSubscription.RetryCount = subscription.RetryCount;
        updateSubscription.LastErrorMessage = subscription.LastErrorMessage;
        updateSubscription.Statistics = subscription.Statistics;

        await _rep.UpdateAsync(updateSubscription);
    }

    /// <summary>
    /// 删除MQTT订阅
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task DeleteAsync(DeleteMqttSubscriptionInput input)
    {
        var subscription = await _rep.GetByIdAsync(input.Id);
        if (subscription == null)
            throw Oops.Oh("订阅不存在");

        await _rep.DeleteAsync(subscription);
        
        // 更新主题订阅者数量
        await UpdateTopicSubscriberCount(subscription.InstanceId, subscription.TopicName, -1);
        
        // 更新客户端订阅数量
        await UpdateClientSubscriptionCount(subscription.InstanceId, subscription.ClientId, -1);
    }

    /// <summary>
    /// 批量删除MQTT订阅
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "BatchDelete")]
    public async Task BatchDeleteAsync(BatchDeleteMqttSubscriptionInput input)
    {
        if (input.Ids == null || !input.Ids.Any())
            throw Oops.Oh("请选择要删除的订阅");

        var subscriptions = await _rep.GetListAsync(x => input.Ids.Contains(x.Id));
        
        await _rep.DeleteByIdsAsync(input.Ids.Cast<dynamic>().ToArray());
        
        // 更新统计数据
        foreach (var subscription in subscriptions)
        {
            await UpdateTopicSubscriberCount(subscription.InstanceId, subscription.TopicName, -1);
            await UpdateClientSubscriptionCount(subscription.InstanceId, subscription.ClientId, -1);
        }
    }

    /// <summary>
    /// 启用/禁用MQTT订阅
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "ToggleStatus")]
    public async Task ToggleStatusAsync(ToggleSubscriptionStatusInput input)
    {
        var subscription = await _rep.GetByIdAsync(input.Id);
        if (subscription == null)
            throw Oops.Oh("订阅不存在");

        subscription.IsEnabled = input.IsEnabled;
        
        // 更新订阅状态
        if (input.IsEnabled)
        {
            if (subscription.Status == SubscriptionStatusEnum.Disabled)
                subscription.Status = SubscriptionStatusEnum.Subscribed;
        }
        else
        {
            subscription.Status = SubscriptionStatusEnum.Disabled;
        }

        await _rep.UpdateAsync(subscription);
    }

    /// <summary>
    /// 更新订阅状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "UpdateStatus")]
    public async Task UpdateStatusAsync(UpdateSubscriptionStatusInput input)
    {
        var subscription = await _rep.GetFirstAsync(x => 
            x.ClientId == input.ClientId && 
            x.TopicName == input.TopicName && 
            x.InstanceId == input.InstanceId);
        
        if (subscription == null)
        {
            // 如果订阅不存在且状态为已订阅，自动创建
            if (input.Status == SubscriptionStatusEnum.Subscribed)
            {
                subscription = new MqttSubscription
                {
                    InstanceId = input.InstanceId,
                    ClientId = input.ClientId,
                    TopicName = input.TopicName,
                    QosLevel = input.QosLevel ?? QosLevelEnum.AtMostOnce,
                    Status = SubscriptionStatusEnum.Subscribed,
                    SubscribeTime = DateTime.Now,
                    IsEnabled = true,
                    MessageReceived = 0,
                    MessageDropped = 0,
                    BytesReceived = 0,
                    CurrentQueueLength = 0,
                    RetryCount = 0
                };
                
                // 处理共享订阅
                if (input.TopicName.StartsWith("$share/"))
                {
                    subscription.IsSharedSubscription = true;
                    var parts = input.TopicName.Split('/');
                    if (parts.Length >= 3)
                    {
                        subscription.SharedGroupName = parts[1];
                    }
                }
                
                await _rep.InsertAsync(subscription);
                
                // 更新统计数据
                await UpdateTopicSubscriberCount(input.InstanceId, input.TopicName, 1);
                await UpdateClientSubscriptionCount(input.InstanceId, input.ClientId, 1);
            }
            return;
        }

        var oldStatus = subscription.Status;
        subscription.Status = input.Status;
        subscription.LastActivityTime = DateTime.Now;
        
        switch (input.Status)
        {
            case SubscriptionStatusEnum.Subscribed:
                if (oldStatus != SubscriptionStatusEnum.Subscribed)
                {
                    subscription.SubscribeTime = DateTime.Now;
                    subscription.UnsubscribeTime = null;
                }
                break;
                
            case SubscriptionStatusEnum.Unsubscribed:
                subscription.UnsubscribeTime = DateTime.Now;
                break;
                
            case SubscriptionStatusEnum.Failed:
                subscription.LastErrorMessage = input.ErrorMessage;
                subscription.RetryCount++;
                break;
        }

        await _rep.UpdateAsync(subscription);
        
        // 更新统计数据
        if (oldStatus == SubscriptionStatusEnum.Subscribed && input.Status != SubscriptionStatusEnum.Subscribed)
        {
            await UpdateTopicSubscriberCount(input.InstanceId, input.TopicName, -1);
        }
        else if (oldStatus != SubscriptionStatusEnum.Subscribed && input.Status == SubscriptionStatusEnum.Subscribed)
        {
            await UpdateTopicSubscriberCount(input.InstanceId, input.TopicName, 1);
        }
    }

    /// <summary>
    /// 更新订阅统计信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "UpdateStatistics")]
    public async Task UpdateStatisticsAsync(UpdateSubscriptionStatisticsInput input)
    {
        var subscription = await _rep.GetFirstAsync(x => 
            x.ClientId == input.ClientId && 
            x.TopicName == input.TopicName && 
            x.InstanceId == input.InstanceId);
        
        if (subscription == null)
            return;

        // 更新统计信息
        if (input.MessageReceived.HasValue)
            subscription.MessageReceived += input.MessageReceived.Value;
        
        if (input.MessageDropped.HasValue)
            subscription.MessageDropped += input.MessageDropped.Value;
        
        if (input.BytesReceived.HasValue)
            subscription.BytesReceived += input.BytesReceived.Value;
        
        if (!string.IsNullOrWhiteSpace(input.LastMessageId))
        {
            subscription.LastMessageId = input.LastMessageId;
            subscription.LastMessageTime = DateTime.Now;
        }
        
        if (input.CurrentQueueLength.HasValue)
            subscription.CurrentQueueLength = input.CurrentQueueLength.Value;

        subscription.LastActivityTime = DateTime.Now;

        await _rep.UpdateAsync(subscription);
    }

    /// <summary>
    /// 获取订阅统计信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetStatistics")]
    public async Task<SubscriptionStatisticsOutput> GetStatisticsAsync(GetSubscriptionStatisticsInput input)
    {
        var query = _rep.AsQueryable()
            .WhereIF(input.InstanceId.HasValue, x => x.InstanceId == input.InstanceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.ClientId), x => x.ClientId == input.ClientId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.TopicName), x => x.TopicName == input.TopicName)
            .WhereIF(input.Status.HasValue, x => x.Status == input.Status)
            .WhereIF(input.IsEnabled.HasValue, x => x.IsEnabled == input.IsEnabled);

        var totalCount = await query.CountAsync();
        var activeCount = await query.CountAsync(x => x.Status == SubscriptionStatusEnum.Subscribed);
        var totalMessages = await query.SumAsync(x => x.MessageReceived);
        var totalBytes = await query.SumAsync(x => x.BytesReceived);
        var totalDropped = await query.SumAsync(x => x.MessageDropped);

        var topSubscriptions = await query
            .OrderBy(x => x.MessageReceived, OrderByType.Desc)
            .Take(10)
            .Select(x => new TopSubscriptionOutput
            {
                ClientId = x.ClientId,
                TopicName = x.TopicName,
                MessageReceived = x.MessageReceived,
                BytesReceived = x.BytesReceived,
                MessageDropped = x.MessageDropped
            })
            .ToListAsync();

        return new SubscriptionStatisticsOutput
        {
            TotalSubscriptions = totalCount,
            ActiveSubscriptions = activeCount,
            TotalMessages = totalMessages,
            TotalBytes = totalBytes,
            TotalDropped = totalDropped,
            TopSubscriptions = topSubscriptions
        };
    }

    /// <summary>
    /// 获取客户端订阅列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetClientSubscriptions")]
    public async Task<List<ClientSubscriptionOutput>> GetClientSubscriptionsAsync(GetClientSubscriptionsInput input)
    {
        var query = _rep.AsQueryable()
            .Where(x => x.ClientId == input.ClientId && x.InstanceId == input.InstanceId)
            .WhereIF(input.Status.HasValue, x => x.Status == input.Status)
            .WhereIF(input.IsEnabled.HasValue, x => x.IsEnabled == input.IsEnabled)
            .OrderBy(x => x.TopicName);

        return await query.Select(x => new ClientSubscriptionOutput
        {
            Id = x.Id,
            TopicName = x.TopicName,
            QosLevel = x.QosLevel,
            Status = x.Status,
            SubscribeTime = x.SubscribeTime,
            MessageReceived = x.MessageReceived,
            BytesReceived = x.BytesReceived,
            IsSharedSubscription = x.IsSharedSubscription,
            SharedGroupName = x.SharedGroupName,
            IsEnabled = x.IsEnabled
        }).ToListAsync();
    }

    /// <summary>
    /// 获取主题订阅者列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetTopicSubscribers")]
    public async Task<List<TopicSubscriberOutput>> GetTopicSubscribersAsync(GetTopicSubscribersInput input)
    {
        var query = _rep.AsQueryable()
            .LeftJoin<MqttClient>((s, c) => s.ClientId == c.ClientId && s.InstanceId == c.InstanceId)
            .Where((s, c) => s.TopicName == input.TopicName && s.InstanceId == input.InstanceId)
            .WhereIF(input.Status.HasValue, (s, c) => s.Status == input.Status)
            .WhereIF(input.IsEnabled.HasValue, (s, c) => s.IsEnabled == input.IsEnabled)
            .OrderBy((s, c) => s.SubscribeTime, OrderByType.Desc);

        return await query.Select((s, c) => new TopicSubscriberOutput
        {
            Id = s.Id,
            ClientId = s.ClientId,
            DeviceName = c.DeviceName,
            QosLevel = s.QosLevel,
            Status = s.Status,
            SubscribeTime = s.SubscribeTime,
            MessageReceived = s.MessageReceived,
            BytesReceived = s.BytesReceived,
            IsSharedSubscription = s.IsSharedSubscription,
            SharedGroupName = s.SharedGroupName,
            IsEnabled = s.IsEnabled
        }).ToListAsync();
    }

    #region 私有方法

    /// <summary>
    /// 更新主题订阅者数量
    /// </summary>
    /// <param name="instanceId"></param>
    /// <param name="topicName"></param>
    /// <param name="delta"></param>
    /// <returns></returns>
    private async Task UpdateTopicSubscriberCount(long instanceId, string topicName, int delta)
    {
        try
        {
            var topic = await _topicRep.GetFirstAsync(x => x.TopicName == topicName && x.InstanceId == instanceId);
            if (topic != null)
            {
                topic.SubscriberCount = Math.Max(0, topic.SubscriberCount + delta);
                await _topicRep.UpdateAsync(topic);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新主题订阅者数量失败：{TopicName}, Delta: {Delta}", topicName, delta);
        }
    }

    /// <summary>
    /// 更新客户端订阅数量
    /// </summary>
    /// <param name="instanceId"></param>
    /// <param name="clientId"></param>
    /// <param name="delta"></param>
    /// <returns></returns>
    private async Task UpdateClientSubscriptionCount(long instanceId, string clientId, int delta)
    {
        try
        {
            var client = await _clientRep.GetFirstAsync(x => x.ClientId == clientId && x.InstanceId == instanceId);
            if (client != null)
            {
                client.SubscriptionCount = Math.Max(0, client.SubscriptionCount + delta);
                await _clientRep.UpdateAsync(client);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新客户端订阅数量失败：{ClientId}, Delta: {Delta}", clientId, delta);
        }
    }

    #endregion
}

#region 输入输出模型

/// <summary>
/// 分页查询MQTT订阅输入
/// </summary>
public class PageMqttSubscriptionInput : BasePageInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    public long? InstanceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    public string? TopicName { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public QosLevelEnum? QosLevel { get; set; }

    /// <summary>
    /// 订阅状态
    /// </summary>
    public SubscriptionStatusEnum? Status { get; set; }

    /// <summary>
    /// 是否共享订阅
    /// </summary>
    public bool? IsSharedSubscription { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }
}

/// <summary>
/// 添加MQTT订阅输入
/// </summary>
public class AddMqttSubscriptionInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required(ErrorMessage = "客户端ID不能为空")]
    [MaxLength(128, ErrorMessage = "客户端ID长度不能超过128个字符")]
    public string ClientId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [Required(ErrorMessage = "主题名称不能为空")]
    [MaxLength(512, ErrorMessage = "主题名称长度不能超过512个字符")]
    public string TopicName { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public QosLevelEnum QosLevel { get; set; } = QosLevelEnum.AtMostOnce;

    /// <summary>
    /// 订阅选项
    /// </summary>
    public string? SubscriptionOptions { get; set; }

    /// <summary>
    /// 消息过滤器
    /// </summary>
    public string? MessageFilter { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 优先级
    /// </summary>
    [Range(0, 255, ErrorMessage = "优先级范围必须在0-255之间")]
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 最大队列长度
    /// </summary>
    [Range(1, 100000, ErrorMessage = "最大队列长度范围必须在1-100000之间")]
    public int MaxQueueLength { get; set; } = 1000;

    /// <summary>
    /// 消息处理策略
    /// </summary>
    [MaxLength(50, ErrorMessage = "消息处理策略长度不能超过50个字符")]
    public string? MessageHandlingStrategy { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public string? ExtendedProperties { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 更新MQTT订阅输入
/// </summary>
public class UpdateMqttSubscriptionInput : AddMqttSubscriptionInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 删除MQTT订阅输入
/// </summary>
public class DeleteMqttSubscriptionInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 批量删除MQTT订阅输入
/// </summary>
public class BatchDeleteMqttSubscriptionInput
{
    /// <summary>
    /// 主键ID集合
    /// </summary>
    [Required(ErrorMessage = "主键ID集合不能为空")]
    public List<long> Ids { get; set; }
}

/// <summary>
/// 切换订阅状态输入
/// </summary>
public class ToggleSubscriptionStatusInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}

/// <summary>
/// 更新订阅状态输入
/// </summary>
public class UpdateSubscriptionStatusInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required(ErrorMessage = "客户端ID不能为空")]
    public string ClientId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [Required(ErrorMessage = "主题名称不能为空")]
    public string TopicName { get; set; }

    /// <summary>
    /// 订阅状态
    /// </summary>
    public SubscriptionStatusEnum Status { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public QosLevelEnum? QosLevel { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 更新订阅统计信息输入
/// </summary>
public class UpdateSubscriptionStatisticsInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required(ErrorMessage = "客户端ID不能为空")]
    public string ClientId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [Required(ErrorMessage = "主题名称不能为空")]
    public string TopicName { get; set; }

    /// <summary>
    /// 接收消息数量
    /// </summary>
    public long? MessageReceived { get; set; }

    /// <summary>
    /// 丢弃消息数量
    /// </summary>
    public long? MessageDropped { get; set; }

    /// <summary>
    /// 接收字节数量
    /// </summary>
    public long? BytesReceived { get; set; }

    /// <summary>
    /// 最后消息ID
    /// </summary>
    public string? LastMessageId { get; set; }

    /// <summary>
    /// 当前队列长度
    /// </summary>
    public int? CurrentQueueLength { get; set; }
}

/// <summary>
/// 获取订阅统计信息输入
/// </summary>
public class GetSubscriptionStatisticsInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    public long? InstanceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    public string? TopicName { get; set; }

    /// <summary>
    /// 订阅状态
    /// </summary>
    public SubscriptionStatusEnum? Status { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }
}

/// <summary>
/// 获取客户端订阅列表输入
/// </summary>
public class GetClientSubscriptionsInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required(ErrorMessage = "客户端ID不能为空")]
    public string ClientId { get; set; }

    /// <summary>
    /// 订阅状态
    /// </summary>
    public SubscriptionStatusEnum? Status { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }
}

/// <summary>
/// 获取主题订阅者列表输入
/// </summary>
public class GetTopicSubscribersInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [Required(ErrorMessage = "主题名称不能为空")]
    public string TopicName { get; set; }

    /// <summary>
    /// 订阅状态
    /// </summary>
    public SubscriptionStatusEnum? Status { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }
}

/// <summary>
/// MQTT订阅分页输出
/// </summary>
public class MqttSubscriptionPageOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 实例ID
    /// </summary>
    public long InstanceId { get; set; }

    /// <summary>
    /// 实例名称
    /// </summary>
    public string InstanceName { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; }

    /// <summary>
    /// 主题类型
    /// </summary>
    public TopicTypeEnum? TopicType { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public QosLevelEnum QosLevel { get; set; }

    /// <summary>
    /// 订阅状态
    /// </summary>
    public SubscriptionStatusEnum Status { get; set; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime? SubscribeTime { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime? LastActivityTime { get; set; }

    /// <summary>
    /// 接收消息数量
    /// </summary>
    public long MessageReceived { get; set; }

    /// <summary>
    /// 丢弃消息数量
    /// </summary>
    public long MessageDropped { get; set; }

    /// <summary>
    /// 接收字节数量
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 是否共享订阅
    /// </summary>
    public bool IsSharedSubscription { get; set; }

    /// <summary>
    /// 共享组名称
    /// </summary>
    public string? SharedGroupName { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}

/// <summary>
/// 订阅统计信息输出
/// </summary>
public class SubscriptionStatisticsOutput
{
    /// <summary>
    /// 订阅总数
    /// </summary>
    public int TotalSubscriptions { get; set; }

    /// <summary>
    /// 活跃订阅数
    /// </summary>
    public int ActiveSubscriptions { get; set; }

    /// <summary>
    /// 消息总数
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// 字节总数
    /// </summary>
    public long TotalBytes { get; set; }

    /// <summary>
    /// 丢弃消息总数
    /// </summary>
    public long TotalDropped { get; set; }

    /// <summary>
    /// 热门订阅列表
    /// </summary>
    public List<TopSubscriptionOutput> TopSubscriptions { get; set; } = new();
}

/// <summary>
/// 热门订阅输出
/// </summary>
public class TopSubscriptionOutput
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; }

    /// <summary>
    /// 接收消息数量
    /// </summary>
    public long MessageReceived { get; set; }

    /// <summary>
    /// 接收字节数量
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 丢弃消息数量
    /// </summary>
    public long MessageDropped { get; set; }
}

/// <summary>
/// 客户端订阅输出
/// </summary>
public class ClientSubscriptionOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public QosLevelEnum QosLevel { get; set; }

    /// <summary>
    /// 订阅状态
    /// </summary>
    public SubscriptionStatusEnum Status { get; set; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime? SubscribeTime { get; set; }

    /// <summary>
    /// 接收消息数量
    /// </summary>
    public long MessageReceived { get; set; }

    /// <summary>
    /// 接收字节数量
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 是否共享订阅
    /// </summary>
    public bool IsSharedSubscription { get; set; }

    /// <summary>
    /// 共享组名称
    /// </summary>
    public string? SharedGroupName { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}

/// <summary>
/// 主题订阅者输出
/// </summary>
public class TopicSubscriberOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public QosLevelEnum QosLevel { get; set; }

    /// <summary>
    /// 订阅状态
    /// </summary>
    public SubscriptionStatusEnum Status { get; set; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime? SubscribeTime { get; set; }

    /// <summary>
    /// 接收消息数量
    /// </summary>
    public long MessageReceived { get; set; }

    /// <summary>
    /// 接收字节数量
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 是否共享订阅
    /// </summary>
    public bool IsSharedSubscription { get; set; }

    /// <summary>
    /// 共享组名称
    /// </summary>
    public string? SharedGroupName { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}

#endregion