/*
 修复后的MQTT中台数据库脚本
 主要修复内容：
 1. 修复外键数据类型不匹配问题
 2. 优化索引顺序避免死锁
 3. 调整外键约束策略
 4. 统一字段定义
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mqtt_instance
-- ----------------------------
DROP TABLE IF EXISTS `mqtt_instance`;
CREATE TABLE `mqtt_instance`  (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `InstanceName` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `InstanceCode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `InstanceType` int(11) NOT NULL DEFAULT 1,
  `ServerHost` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `ServerPort` int(11) NOT NULL DEFAULT 1883,
  `ApiHost` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `ApiPort` int(11) NOT NULL DEFAULT 18083,
  `ApiUsername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `ApiPassword` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `Status` int(11) NOT NULL DEFAULT 1,
  `EnableSSL` bit(1) NOT NULL DEFAULT b'0',
  `SSLPort` int(11) NULL DEFAULT NULL,
  `EnableWebSocket` bit(1) NOT NULL DEFAULT b'0',
  `WebSocketPort` int(11) NULL DEFAULT NULL,
  `WssPort` int(11) NOT NULL DEFAULT 8084 COMMENT 'WebSocket SSL端口',
  `ConnectionCount` int(11) NOT NULL DEFAULT 0,
  `MessageCount` bigint(20) NOT NULL DEFAULT 0,
  `ByteCount` bigint(20) NOT NULL DEFAULT 0,
  `MaxConnections` int(11) NOT NULL DEFAULT 1000000 COMMENT '最大连接数',
  `CurrentConnections` int(11) NOT NULL DEFAULT 0 COMMENT '当前连接数',
  `TotalMessages` bigint(20) NOT NULL DEFAULT 0 COMMENT '总消息数',
  `TotalBytes` bigint(20) NOT NULL DEFAULT 0 COMMENT '总字节数',
  `AliyunCompatible` tinyint(1) NULL DEFAULT 0,
  `AliyunProductKey` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `AliyunRegionId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `AliyunInstanceId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `DeviceIdPrefix` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'device_',
  `GroupIdPrefix` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'group_',
  `IsEnabled` bit(1) NOT NULL DEFAULT b'1',
  `LastHeartbeatTime` datetime NULL DEFAULT NULL COMMENT '最后心跳时间',
  `ConfigJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `CreateTime` datetime NULL DEFAULT NULL,
  `UpdateTime` datetime NULL DEFAULT NULL,
  `CreateUserId` bigint(20) NULL DEFAULT NULL,
  `CreateUserName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `UpdateUserId` bigint(20) NULL DEFAULT NULL,
  `UpdateUserName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `IsDelete` bit(1) NOT NULL DEFAULT b'0',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_mqtt_instance_code`(`InstanceCode` ASC) USING BTREE,
  INDEX `IX_mqtt_instance_type`(`InstanceType` ASC) USING BTREE,
  INDEX `IX_mqtt_instance_status`(`Status` ASC, `IsEnabled` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'MQTT实例' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mqtt_client
-- ----------------------------
DROP TABLE IF EXISTS `mqtt_client`;
CREATE TABLE `mqtt_client`  (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `InstanceId` bigint(20) NOT NULL,
  `ClientId` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `DeviceName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备名称',
  `ClientName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `ClientType` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `DeviceId` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `GroupId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `ProductKey` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `DeviceSecret` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `Status` int(11) NOT NULL DEFAULT 1,
  `IsOnline` bit(1) NOT NULL DEFAULT b'0',
  `IpAddress` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `Port` int(11) NULL DEFAULT NULL COMMENT '客户端端口',
  `UserAgent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `KeepAlive` int(11) NULL DEFAULT 60,
  `CleanSession` tinyint(1) NOT NULL DEFAULT 1,
  `ProtocolVersion` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `AuthMode` int(11) NOT NULL DEFAULT 1 COMMENT '认证方式',
  `ConnectedAt` datetime NULL DEFAULT NULL COMMENT '连接时间',
  `DisconnectedAt` datetime NULL DEFAULT NULL COMMENT '断开时间',
  `LastActivity` datetime NULL DEFAULT NULL COMMENT '最后活动时间',
  `ConnectCount` bigint(20) NULL DEFAULT 0,
  `MessagesSent` bigint(20) NOT NULL DEFAULT 0 COMMENT '发送消息数',
  `MessagesReceived` bigint(20) NOT NULL DEFAULT 0 COMMENT '接收消息数',
  `BytesSent` bigint(20) NOT NULL DEFAULT 0,
  `BytesReceived` bigint(20) NOT NULL DEFAULT 0,
  `SubscriptionCount` int(11) NOT NULL DEFAULT 0 COMMENT '订阅数量',
  `MaxSubscriptions` int(11) NOT NULL DEFAULT 100 COMMENT '最大订阅数量',
  `DeviceType` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备类型',
  `DeviceVersion` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备版本',
  `DeviceTags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `LocationInfo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `LastError` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后错误信息',
  `IsEnabled` bit(1) NOT NULL DEFAULT b'1',
  `ConfigJson` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `ExtendedProperties` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `Remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `CreateTime` datetime NULL DEFAULT NULL,
  `UpdateTime` datetime NULL DEFAULT NULL,
  `CreateUserId` bigint(20) NULL DEFAULT NULL,
  `CreateUserName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `UpdateUserId` bigint(20) NULL DEFAULT NULL,
  `UpdateUserName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `IsDelete` bit(1) NOT NULL DEFAULT b'0',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_mqtt_client_instance`(`InstanceId` ASC) USING BTREE,
  UNIQUE INDEX `IX_mqtt_client_instance_clientid`(`InstanceId` ASC, `ClientId` ASC) USING BTREE,
  INDEX `IX_mqtt_client_device`(`DeviceId` ASC, `GroupId` ASC) USING BTREE,
  INDEX `IX_mqtt_client_status`(`Status` ASC, `IsEnabled` ASC) USING BTREE,
  INDEX `IX_mqtt_client_online`(`IsOnline` ASC) USING BTREE,
  CONSTRAINT `FK_mqtt_client_instance` FOREIGN KEY (`InstanceId`) REFERENCES `mqtt_instance` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'MQTT客户端' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mqtt_topic
-- ----------------------------
DROP TABLE IF EXISTS `mqtt_topic`;
CREATE TABLE `mqtt_topic`  (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `InstanceId` bigint(20) NOT NULL,
  `TopicName` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `TopicType` int(11) NOT NULL DEFAULT 1,
  `QosLevel` int(11) NOT NULL DEFAULT 0,
  `RetainMessage` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否保留消息',
  `SubscriptionCount` int(11) NOT NULL DEFAULT 0 COMMENT '订阅数量',
  `MessageCount` bigint(20) NOT NULL DEFAULT 0,
  `ByteCount` bigint(20) NOT NULL DEFAULT 0,
  `LastMessageTime` datetime NULL DEFAULT NULL,
  `LastMessageContent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后消息内容',
  `MaxMessageSize` int(11) NOT NULL DEFAULT 1048576 COMMENT '最大消息大小',
  `MessageExpiry` int(11) NOT NULL DEFAULT 0 COMMENT '消息过期时间',
  `AllowPublish` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否允许发布',
  `AllowSubscribe` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否允许订阅',
  `PublishPermissions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '发布权限',
  `SubscribePermissions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '订阅权限',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主题描述',
  `TopicTags` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '主题标签',
  `IsSystemTopic` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统主题',
  `MonitorConfig` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '监控配置',
  `AlertRules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '告警规则',
  `ForwardRules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '数据转发规则',
  `IsEnabled` bit(1) NOT NULL DEFAULT b'1',
  `ConfigJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `ExtendedProperties` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '扩展属性',
  `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `CreateTime` datetime NULL DEFAULT NULL,
  `UpdateTime` datetime NULL DEFAULT NULL,
  `CreateUserId` bigint(20) NULL DEFAULT NULL,
  `CreateUserName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `UpdateUserId` bigint(20) NULL DEFAULT NULL,
  `UpdateUserName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `IsDelete` bit(1) NOT NULL DEFAULT b'0',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_mqtt_topic_instance`(`InstanceId` ASC) USING BTREE,
  UNIQUE INDEX `IX_mqtt_topic_instance_name`(`InstanceId` ASC, `TopicName` ASC) USING BTREE,
  INDEX `IX_mqtt_topic_type`(`TopicType` ASC) USING BTREE,
  INDEX `IX_mqtt_topic_qos`(`QosLevel` ASC) USING BTREE,
  CONSTRAINT `FK_mqtt_topic_instance` FOREIGN KEY (`InstanceId`) REFERENCES `mqtt_instance` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'MQTT主题' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mqtt_client_auth
-- ----------------------------
DROP TABLE IF EXISTS `mqtt_client_auth`;
CREATE TABLE `mqtt_client_auth`  (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `InstanceId` bigint(20) NOT NULL,
  `ClientId` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '客户端ID字符串',
  `ClientRefId` bigint(20) NULL DEFAULT NULL COMMENT '客户端表引用ID',
  `AuthMode` int(11) NOT NULL DEFAULT 1,
  `Username` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `Password` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码',
  `PasswordHash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `Salt` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `AccessKey` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `SecretKey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `SignMethod` int(11) NULL DEFAULT 1,
  `JwtToken` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `JwtSecret` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'JWT密钥',
  `JwtExpiry` int(11) NULL DEFAULT NULL COMMENT 'JWT过期时间',
  `CertificateData` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `PrivateKey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '私钥内容',
  `CaCertificate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'CA证书内容',
  `AllowedIPs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `DeniedIPs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `IpWhitelist` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP白名单',
  `IpBlacklist` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP黑名单',
  `AllowedTopics` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '允许的主题列表',
  `DeniedTopics` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '禁止的主题列表',
  `MaxConnections` int(11) NULL DEFAULT 1,
  `CurrentConnections` int(11) NOT NULL DEFAULT 0 COMMENT '当前连接数',
  `RateLimit` int(11) NULL DEFAULT 1000,
  `MessageRateLimit` int(11) NOT NULL DEFAULT 1000 COMMENT '消息速率限制',
  `ByteRateLimit` bigint(20) NOT NULL DEFAULT 1048576 COMMENT '字节速率限制',
  `ConnectionTimeout` int(11) NOT NULL DEFAULT 60 COMMENT '连接超时时间',
  `MaxMessageSize` int(11) NOT NULL DEFAULT 1048576 COMMENT '最大消息大小',
  `MaxSubscriptions` int(11) NOT NULL DEFAULT 100 COMMENT '最大订阅数量',
  `IsLocked` tinyint(1) NOT NULL DEFAULT 0,
  `LockReason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `LockTime` datetime NULL DEFAULT NULL,
  `LockDuration` int(11) NOT NULL DEFAULT 3600 COMMENT '锁定时长',
  `UnlockTime` datetime NULL DEFAULT NULL,
  `LoginAttempts` int(11) NOT NULL DEFAULT 0,
  `MaxFailedCount` int(11) NOT NULL DEFAULT 5 COMMENT '最大失败次数',
  `LastLoginTime` datetime NULL DEFAULT NULL,
  `LastLoginIP` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `LastAuthTime` datetime NULL DEFAULT NULL COMMENT '最后认证时间',
  `AuthSuccessCount` bigint(20) NOT NULL DEFAULT 0,
  `AuthFailureCount` bigint(20) NOT NULL DEFAULT 0,
  `SuccessCount` int(11) NOT NULL DEFAULT 0 COMMENT '认证成功次数',
  `FailedCount` int(11) NOT NULL DEFAULT 0 COMMENT '认证失败次数',
  `ExpireTime` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `IsEnabled` bit(1) NOT NULL DEFAULT b'1',
  `ConfigJson` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `ExtendedProperties` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展属性',
  `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `CreateTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UpdateTime` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `CreateUserId` bigint(20) NULL DEFAULT NULL COMMENT '创建者Id',
  `CreateUserName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者姓名',
  `UpdateUserId` bigint(20) NULL DEFAULT NULL COMMENT '修改者Id',
  `UpdateUserName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改者姓名',
  `IsDelete` bit(1) NOT NULL DEFAULT b'0' COMMENT '软删除',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_mqtt_client_auth_instance`(`InstanceId` ASC) USING BTREE,
  INDEX `IX_mqtt_client_auth_client_ref`(`ClientRefId` ASC) USING BTREE,
  UNIQUE INDEX `IX_mqtt_client_auth_client_instance`(`ClientId` ASC, `InstanceId` ASC) USING BTREE,
  INDEX `IX_mqtt_client_auth_mode`(`AuthMode` ASC) USING BTREE,
  INDEX `IX_mqtt_client_auth_username`(`Username` ASC) USING BTREE,
  INDEX `IX_mqtt_client_auth_enabled`(`IsEnabled` ASC) USING BTREE,
  CONSTRAINT `FK_mqtt_client_auth_instance` FOREIGN KEY (`InstanceId`) REFERENCES `mqtt_instance` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_mqtt_client_auth_client_ref` FOREIGN KEY (`ClientRefId`) REFERENCES `mqtt_client` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'MQTT客户端验证' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mqtt_subscription
-- ----------------------------
DROP TABLE IF EXISTS `mqtt_subscription`;
CREATE TABLE `mqtt_subscription`  (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `InstanceId` bigint(20) NOT NULL,
  `ClientId` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端ID字符串',
  `ClientRefId` bigint(20) NULL DEFAULT NULL COMMENT '客户端表引用ID',
  `TopicName` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主题名称',
  `TopicId` bigint(20) NULL DEFAULT NULL COMMENT '主题ID',
  `QosLevel` int(11) NOT NULL DEFAULT 0,
  `Status` int(11) NOT NULL DEFAULT 1,
  `SubscribedAt` datetime NULL DEFAULT NULL COMMENT '订阅时间',
  `UnsubscribedAt` datetime NULL DEFAULT NULL COMMENT '取消订阅时间',
  `LastActivity` datetime NULL DEFAULT NULL COMMENT '最后活动时间',
  `MessageCount` bigint(20) NOT NULL DEFAULT 0,
  `MessageReceived` bigint(20) NOT NULL DEFAULT 0 COMMENT '接收消息数',
  `BytesReceived` bigint(20) NOT NULL DEFAULT 0 COMMENT '接收字节数',
  `MessagesDropped` bigint(20) NOT NULL DEFAULT 0 COMMENT '丢弃消息数',
  `LastMessageTime` datetime NULL DEFAULT NULL,
  `LastMessageId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后消息ID',
  `IsSharedSubscription` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否共享订阅',
  `SharedGroupName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '共享组名称',
  `SubscriptionOptions` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `MessageFilter` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `Priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级',
  `MaxQueueLength` int(11) NOT NULL DEFAULT 1000 COMMENT '最大队列长度',
  `CurrentQueueLength` int(11) NOT NULL DEFAULT 0 COMMENT '当前队列长度',
  `HandlingStrategy` int(11) NOT NULL DEFAULT 1 COMMENT '消息处理策略',
  `RetryCount` int(11) NOT NULL DEFAULT 0 COMMENT '重试次数',
  `MaxRetryCount` int(11) NOT NULL DEFAULT 3 COMMENT '最大重试次数',
  `LastError` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后错误信息',
  `LastErrorMessage` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后错误消息',
  `Statistics` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `IsEnabled` bit(1) NOT NULL DEFAULT b'1',
  `ConfigJson` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `ExtendedProperties` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `CreateTime` datetime NULL DEFAULT NULL,
  `UpdateTime` datetime NULL DEFAULT NULL,
  `CreateUserId` bigint(20) NULL DEFAULT NULL,
  `CreateUserName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `UpdateUserId` bigint(20) NULL DEFAULT NULL,
  `UpdateUserName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `IsDelete` bit(1) NOT NULL DEFAULT b'0',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_mqtt_subscription_instance`(`InstanceId` ASC) USING BTREE,
  INDEX `IX_mqtt_subscription_client_ref`(`ClientRefId` ASC) USING BTREE,
  INDEX `IX_mqtt_subscription_topic_id`(`TopicId` ASC) USING BTREE,
  UNIQUE INDEX `IX_mqtt_subscription_client_topic_name`(`ClientId` ASC, `TopicName` ASC) USING BTREE,
  INDEX `IX_mqtt_subscription_status`(`Status` ASC) USING BTREE,
  CONSTRAINT `FK_mqtt_subscription_instance` FOREIGN KEY (`InstanceId`) REFERENCES `mqtt_instance` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_mqtt_subscription_client_ref` FOREIGN KEY (`ClientRefId`) REFERENCES `mqtt_client` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_mqtt_subscription_topic` FOREIGN KEY (`TopicId`) REFERENCES `mqtt_topic` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'MQTT订阅表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- 插入默认数据
INSERT INTO `mqtt_instance` (`Id`, `InstanceName`, `InstanceCode`, `InstanceType`, `ServerHost`, `ServerPort`, `ApiHost`, `ApiPort`, `ApiUsername`, `ApiPassword`, `Status`, `EnableSSL`, `SSLPort`, `EnableWebSocket`, `WebSocketPort`, `WssPort`, `DeviceIdPrefix`, `GroupIdPrefix`, `IsEnabled`, `ConfigJson`, `Remark`, `CreateTime`, `IsDelete`) VALUES 
(1, 'Default EMQX Instance', 'default', 1, 'localhost', 1883, 'localhost', 18083, 'admin', 'public', 1, b'0', 8883, b'0', 8083, 8084, 'device_', 'group_', b'1', NULL, 'Default EMQX instance configuration', NOW(), b'0'),
(2, 'Aliyun Compatible Instance', 'aliyun', 8, 'mqtt.aliyun.com', 1883, 'mqtt.aliyun.com', 18083, 'admin', 'admin123', 1, b'0', 8883, b'0', 8083, 8084, 'aliyun_device_', 'aliyun_group_', b'1', NULL, 'Aliyun MQTT compatible instance', NOW(), b'0');