// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Plugin.EMQX.Enum;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Plugin.EMQX.Entity;

/// <summary>
/// MQTT实例表
/// </summary>
[SugarTable("mqtt_instance", "MQTT实例表")]
[SugarIndex("idx_mqtt_instance_name", nameof(InstanceName), OrderByType.Asc)]
[SugarIndex("idx_mqtt_instance_code", nameof(InstanceCode), OrderByType.Asc)]
[SugarIndex("idx_mqtt_instance_status", nameof(Status), OrderByType.Asc)]
[SugarIndex("idx_mqtt_instance_type", nameof(InstanceType), OrderByType.Asc)]
public class MqttInstance : EntityBaseDel
{
    /// <summary>
    /// 实例名称
    /// </summary>
    [SugarColumn(ColumnDescription = "实例名称", Length = 128)]
    [Required, MaxLength(128)]
    public string InstanceName { get; set; }

    /// <summary>
    /// 实例编码（用于区分不同的逻辑实例）
    /// </summary>
    [SugarColumn(ColumnDescription = "实例编码", Length = 64)]
    [Required, MaxLength(64)]
    public string InstanceCode { get; set; }

    /// <summary>
    /// 实例类型（阿里云兼容、标准MQTT等）
    /// </summary>
    [SugarColumn(ColumnDescription = "实例类型")]
    public InstanceTypeEnum InstanceType { get; set; } = InstanceTypeEnum.Standard;

    /// <summary>
    /// EMQX服务器地址
    /// </summary>
    [SugarColumn(ColumnDescription = "EMQX服务器地址", Length = 256)]
    [Required, MaxLength(256)]
    public string ServerHost { get; set; }

    /// <summary>
    /// EMQX服务器端口
    /// </summary>
    [SugarColumn(ColumnDescription = "EMQX服务器端口")]
    public int ServerPort { get; set; } = 1883;

    /// <summary>
    /// EMQX管理API地址
    /// </summary>
    [SugarColumn(ColumnDescription = "EMQX管理API地址", Length = 256)]
    [MaxLength(256)]
    public string? ApiHost { get; set; }

    /// <summary>
    /// EMQX管理API端口
    /// </summary>
    [SugarColumn(ColumnDescription = "EMQX管理API端口")]
    public int ApiPort { get; set; } = 18083;

    /// <summary>
    /// API用户名
    /// </summary>
    [SugarColumn(ColumnDescription = "API用户名", Length = 64)]
    [MaxLength(64)]
    public string? ApiUsername { get; set; }

    /// <summary>
    /// API密码
    /// </summary>
    [SugarColumn(ColumnDescription = "API密码", Length = 128)]
    [MaxLength(128)]
    public string? ApiPassword { get; set; }

    /// <summary>
    /// 实例状态
    /// </summary>
    [SugarColumn(ColumnDescription = "实例状态")]
    public InstanceStatusEnum Status { get; set; } = InstanceStatusEnum.Active;

    /// <summary>
    /// 是否启用SSL/TLS
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用SSL/TLS", ColumnName = "EnableSSL")]
    public bool EnableSsl { get; set; } = false;

    /// <summary>
    /// SSL端口
    /// </summary>
    [SugarColumn(ColumnDescription = "SSL端口", ColumnName = "SSLPort", IsNullable = true)]
    public int? SslPort { get; set; } = 8883;

    /// <summary>
    /// 是否启用WebSocket
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用WebSocket", ColumnName = "EnableWebSocket")]
    public bool EnableWebSocket { get; set; } = false;

    /// <summary>
    /// WebSocket端口
    /// </summary>
    [SugarColumn(ColumnDescription = "WebSocket端口", ColumnName = "WebSocketPort", IsNullable = true)]
    public int? WsPort { get; set; } = 8083;

    /// <summary>
    /// WebSocket SSL端口
    /// </summary>
    [SugarColumn(ColumnDescription = "WebSocket SSL端口")]
    public int WssPort { get; set; } = 8084;

    /// <summary>
    /// 最大连接数
    /// </summary>
    [SugarColumn(ColumnDescription = "最大连接数")]
    public int MaxConnections { get; set; } = 1000000;

    /// <summary>
    /// 当前连接数
    /// </summary>
    [SugarColumn(ColumnDescription = "当前连接数")]
    public int CurrentConnections { get; set; } = 0;

    /// <summary>
    /// 总消息数
    /// </summary>
    [SugarColumn(ColumnDescription = "总消息数")]
    public long TotalMessages { get; set; } = 0;

    /// <summary>
    /// 总字节数
    /// </summary>
    [SugarColumn(ColumnDescription = "总字节数")]
    public long TotalBytes { get; set; } = 0;

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后心跳时间", IsNullable = true)]
    public DateTime? LastHeartbeatTime { get; set; }

    /// <summary>
    /// 连接数统计
    /// </summary>
    [SugarColumn(ColumnDescription = "连接数统计")]
    public int ConnectionCount { get; set; } = 0;

    /// <summary>
    /// 消息数统计
    /// </summary>
    [SugarColumn(ColumnDescription = "消息数统计")]
    public long MessageCount { get; set; } = 0;

    /// <summary>
    /// 字节数统计
    /// </summary>
    [SugarColumn(ColumnDescription = "字节数统计")]
    public long ByteCount { get; set; } = 0;

    /// <summary>
    /// 阿里云兼容配置 - 产品Key
    /// </summary>
    [SugarColumn(ColumnDescription = "阿里云产品Key", Length = 64, IsNullable = true)]
    [MaxLength(64)]
    public string? AliyunProductKey { get; set; }

    /// <summary>
    /// 阿里云兼容配置 - 区域ID
    /// </summary>
    [SugarColumn(ColumnDescription = "阿里云区域ID", Length = 32, IsNullable = true)]
    [MaxLength(32)]
    public string? AliyunRegionId { get; set; }

    /// <summary>
    /// 阿里云兼容配置 - 实例ID
    /// </summary>
    [SugarColumn(ColumnDescription = "阿里云实例ID", Length = 64, IsNullable = true)]
    [MaxLength(64)]
    public string? AliyunInstanceId { get; set; }

    /// <summary>
    /// 设备ID前缀（用于区分不同实例的设备）
    /// </summary>
    [SugarColumn(ColumnDescription = "设备ID前缀", Length = 32)]
    [Required, MaxLength(32)]
    public string DeviceIdPrefix { get; set; }

    /// <summary>
    /// 组ID前缀（阿里云兼容）
    /// </summary>
    [SugarColumn(ColumnDescription = "组ID前缀", Length = 32, IsNullable = true)]
    [MaxLength(32)]
    public string? GroupIdPrefix { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 阿里云兼容模式
    /// </summary>
    [SugarColumn(ColumnDescription = "阿里云兼容模式", IsNullable = true, ColumnName = "AliyunCompatible")]
    public bool? AliyunCompatible { get; set; }

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后心跳时间", IsNullable = true)]
    public DateTime? LastHeartbeat { get; set; }

    /// <summary>
    /// 配置信息（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "配置信息", ColumnDataType = "text", IsNullable = true)]
    public string? ConfigJson { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string? Remark { get; set; }

    /// <summary>
    /// 客户端列表
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(MqttClient.InstanceId))]
    public List<MqttClient>? Clients { get; set; }

    /// <summary>
    /// 主题列表
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(MqttTopic.InstanceId))]
    public List<MqttTopic>? Topics { get; set; }

    /// <summary>
    /// 认证配置列表
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(MqttClientAuth.InstanceId))]
    public List<MqttClientAuth>? AuthConfigs { get; set; }
}