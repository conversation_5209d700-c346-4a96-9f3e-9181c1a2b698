# Admin.NET.Plugin.EMQX 数据库说明

## 概述

本文档说明了 Admin.NET.Plugin.EMQX 插件的 MySQL 数据库结构和使用方法。该插件完全兼容 Admin.NET 框架的数据库设计规范。

## 数据库表结构

### 1. 核心表

| 表名 | 说明 | 主要功能 |
|------|------|----------|
| `mqtt_instance` | MQTT实例表 | 管理EMQX实例的基本信息、连接配置和状态 |
| `mqtt_client` | MQTT客户端表 | 记录连接到MQTT实例的客户端信息和统计数据 |
| `mqtt_topic` | MQTT主题表 | 管理MQTT主题的订阅统计和消息统计 |
| `mqtt_subscription` | MQTT订阅表 | 记录客户端对主题的订阅关系和消息统计 |
| `mqtt_client_auth` | MQTT客户端认证表 | 管理客户端认证配置，支持阿里云签名认证等多种认证方式，包含安全加密和权限控制 |

### 2. 表关系

```
mqtt_instance (1) -----> (N) mqtt_client
mqtt_instance (1) -----> (N) mqtt_topic
mqtt_instance (1) -----> (N) mqtt_subscription
mqtt_instance (1) -----> (N) mqtt_client_auth
```

### 3. 字段说明

#### 公共字段（继承自 EntityBase）
- `Id`: 主键，使用雪花ID
- `CreateTime`: 创建时间
- `UpdateTime`: 更新时间
- `CreateUserId`: 创建者ID
- `CreateUserName`: 创建者姓名
- `UpdateUserId`: 修改者ID
- `UpdateUserName`: 修改者姓名

#### 枚举值说明

**实例状态 (Status)**
- 1: 运行中
- 2: 已停止
- 3: 启动中
- 4: 停止中
- 5: 异常

**客户端连接状态 (Status)**
- 1: 已连接
- 2: 已断开
- 3: 连接中

**QoS等级 (QosLevel)**
- 0: 最多一次 (At Most Once)
- 1: 至少一次 (At Least Once)
- 2: 恰好一次 (Exactly Once)

**认证模式 (AuthMode)**
- 1: 用户名密码认证
- 2: 阿里云签名认证
- 3: JWT认证
- 4: 证书认证

**签名方法 (SignMethod)**
- 1: HMAC-SHA1
- 2: HMAC-SHA256
- 3: HMAC-MD5

## 安装和使用

### 1. 执行数据库脚本

```bash
# 连接到MySQL数据库
mysql -u username -p database_name

# 执行脚本
source mysql_emqx_schema.sql
```

或者使用MySQL客户端工具（如Navicat、phpMyAdmin等）直接执行 `mysql_emqx_schema.sql` 文件。

### 2. 验证安装

脚本执行完成后，会自动显示以下信息：
- 各表的记录数量统计
- 客户端连接统计视图数据
- 主题订阅统计视图数据

### 3. 示例数据

脚本包含了以下示例数据：
- 2个MQTT实例（主实例和测试实例）
- 3个MQTT客户端（包含普通客户端和传感器设备）
- 4个MQTT主题（温度、湿度、系统状态、用户通知）
- 5个订阅关系
- 3个客户端认证配置（包含阿里云签名认证示例）

## 高级功能

### 1. 统计视图

#### 客户端连接统计视图 (`v_mqtt_client_stats`)
提供每个实例的客户端连接统计信息，包括：
- 总客户端数
- 在线客户端数
- 离线客户端数
- 消息和字节统计

#### 主题订阅统计视图 (`v_mqtt_topic_stats`)
提供每个主题的订阅统计信息，包括：
- 订阅数量
- 消息数量
- 活跃订阅数

#### 客户端认证统计视图 (`v_mqtt_client_auth_stats`) 🆕
提供客户端认证统计信息，包括：
- 认证方式分布
- 认证成功率
- 失败次数统计

### 2. 存储过程

#### 更新实例统计 (`sp_update_instance_stats`)
```sql
CALL sp_update_instance_stats(1); -- 更新实例ID为1的统计信息
```

#### 清理过期数据 (`sp_cleanup_expired_data`)
```sql
CALL sp_cleanup_expired_data(30); -- 清理30天前的过期数据
```

#### 验证客户端认证 (`sp_validate_client_auth`) 🆕
```sql
CALL sp_validate_client_auth('client_id', 'username', 'password'); -- 验证客户端认证
```

### 3. 触发器

- `tr_mqtt_client_status_update`: 客户端状态变更时自动更新主题订阅数量

## 阿里云MQTT签名认证 🆕

### 认证原理
基于阿里云MQTT签名认证规范实现，支持以下认证流程：

1. **Username格式**: `Signature|AccessKeyId|InstanceId`
2. **Password计算**: `Base64(HMAC-SHA1(AccessKeySecret, ClientId))`
3. **支持算法**: HMAC-SHA1, HMAC-SHA256, HMAC-MD5

### 使用示例

#### 1. 创建认证配置
```sql
INSERT INTO mqtt_client_auth (
    ClientId, InstanceId, AccessKeyId, AccessKeySecret, 
    SignMethod, AuthMode, IsEnabled
) VALUES (
    'GID_Test@@@0001', 1, 'LTAI5tFhc8AKGfPdmBTHV2nJ', 
    'your_access_key_secret', 1, 2, 1
);
```

#### 2. 生成连接参数
- **ClientId**: `GID_Test@@@0001`
- **Username**: `Signature|LTAI5tFhc8AKGfPdmBTHV2nJ|mqtt-xxxxx`
- **Password**: 通过API计算得出的签名

#### 3. API接口
- `POST /api/MqttClientAuth/ValidateSignature`: 验证签名
- `POST /api/MqttClientAuth/GenerateConnectionParams`: 生成连接参数
- `POST /api/MqttClientAuth/TestSignature`: 测试签名计算

### 安全特性

1. **密码加密**: AccessKey Secret使用MD5+盐值加密存储
2. **失败锁定**: 支持登录失败次数限制和账户锁定
3. **IP控制**: 支持IP白名单和黑名单
4. **主题权限**: 支持允许/禁止主题列表
5. **过期控制**: 支持认证配置过期时间
6. **连接限制**: 支持最大连接数限制

## 性能优化

### 1. 索引策略

脚本已创建了以下关键索引：
- 主键索引
- 外键索引
- 状态字段索引
- 时间字段索引
- 复合唯一索引

### 2. 分区建议（可选）

对于大数据量场景，建议对以下表进行分区：

```sql
-- 按月分区mqtt_client表
ALTER TABLE mqtt_client PARTITION BY RANGE (TO_DAYS(CreateTime)) (
    PARTITION p202401 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    -- 继续添加分区...
);
```

### 3. 数据清理策略

建议定期执行数据清理：

```sql
-- 每周执行一次，清理30天前的过期数据
EVENT scheduler_cleanup_expired_data
ON SCHEDULE EVERY 1 WEEK
DO
  CALL sp_cleanup_expired_data(30);
```

## 注意事项

### 1. 字符集和排序规则
- 使用 `utf8mb4` 字符集
- 使用 `utf8mb4_unicode_ci` 排序规则
- 支持完整的Unicode字符集，包括emoji

### 2. 外键约束
- 启用了外键约束以保证数据完整性
- 使用 `CASCADE` 删除策略
- 删除实例时会自动删除相关的客户端、主题和订阅数据

### 3. 数据类型选择
- 主键使用 `bigint(20)` 支持雪花ID
- 时间字段使用 `datetime` 类型
- 文本字段根据实际需要设置合适的长度
- 统计字段使用 `bigint(20)` 支持大数值

### 4. 兼容性
- 完全兼容 Admin.NET 框架的 EntityBase 基类
- 支持框架的审计功能
- 支持框架的软删除功能（如需要可继承 EntityBaseDel）
- 支持框架的多租户功能（如需要可继承 EntityBaseTenant）

## 扩展建议

### 1. 添加软删除支持

如果需要软删除功能，可以修改实体继承 `EntityBaseDel`：

```csharp
public class MqttInstance : EntityBaseDel
{
    // 实体定义...
}
```

### 2. 添加多租户支持

如果需要多租户功能，可以修改实体继承 `EntityBaseTenant`：

```csharp
public class MqttInstance : EntityBaseTenant
{
    // 实体定义...
}
```

### 3. 添加数据权限支持

如果需要机构数据权限，可以修改实体继承 `EntityBaseOrg`：

```csharp
public class MqttInstance : EntityBaseOrg
{
    // 实体定义...
}
```

## 维护和监控

### 1. 定期维护任务
- 执行 `sp_cleanup_expired_data` 清理过期数据
- 执行 `sp_update_instance_stats` 更新统计信息
- 检查索引使用情况和性能

### 2. 监控指标
- 表大小和增长趋势
- 查询性能和慢查询
- 连接数和并发情况
- 数据一致性检查

### 3. 备份策略
- 定期全量备份
- 增量备份重要数据
- 测试恢复流程

## 技术支持

如有问题，请参考：
1. Admin.NET 框架文档
2. SqlSugar ORM 文档
3. EMQX 官方文档
4. MySQL 官方文档