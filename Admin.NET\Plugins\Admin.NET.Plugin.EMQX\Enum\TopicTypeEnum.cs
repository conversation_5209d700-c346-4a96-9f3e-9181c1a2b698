// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.ComponentModel;

namespace Admin.NET.Plugin.EMQX.Enum;

/// <summary>
/// MQTT主题类型枚举
/// </summary>
[Description("MQTT主题类型")]
public enum TopicTypeEnum
{
    /// <summary>
    /// 普通主题
    /// </summary>
    [Description("普通主题")]
    Normal = 1,

    /// <summary>
    /// 系统主题
    /// </summary>
    [Description("系统主题")]
    System = 2,

    /// <summary>
    /// 共享主题
    /// </summary>
    [Description("共享主题")]
    Shared = 3,

    /// <summary>
    /// 通配符主题
    /// </summary>
    [Description("通配符主题")]
    Wildcard = 4,

    /// <summary>
    /// 保留主题
    /// </summary>
    [Description("保留主题")]
    Retained = 5,

    /// <summary>
    /// 遗嘱主题
    /// </summary>
    [Description("遗嘱主题")]
    Will = 6,

    /// <summary>
    /// 设备影子主题
    /// </summary>
    [Description("设备影子主题")]
    Shadow = 7,

    /// <summary>
    /// 事件主题
    /// </summary>
    [Description("事件主题")]
    Event = 8,

    /// <summary>
    /// 属性主题
    /// </summary>
    [Description("属性主题")]
    Property = 9,

    /// <summary>
    /// 服务主题
    /// </summary>
    [Description("服务主题")]
    Service = 10,

    /// <summary>
    /// 设备影子主题
    /// </summary>
    [Description("设备影子主题")]
    DeviceShadow = 11
}