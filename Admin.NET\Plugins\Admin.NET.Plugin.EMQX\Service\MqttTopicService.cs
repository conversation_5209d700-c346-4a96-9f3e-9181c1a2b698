// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Plugin.EMQX.Entity;
using Admin.NET.Plugin.EMQX.Enum;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Mapster;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Admin.NET.Plugin.EMQX.Service;

/// <summary>
/// MQTT主题服务
/// </summary>
[ApiDescriptionSettings("Plugin-EMQX", Name = "MqttTopic", Order = 400)]
public class MqttTopicService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MqttTopic> _rep;
    private readonly SqlSugarRepository<MqttInstance> _instanceRep;
    private readonly SqlSugarRepository<MqttSubscription> _subscriptionRep;
    private readonly ILogger<MqttTopicService> _logger;

    public MqttTopicService(
        SqlSugarRepository<MqttTopic> rep,
        SqlSugarRepository<MqttInstance> instanceRep,
        SqlSugarRepository<MqttSubscription> subscriptionRep,
        ILogger<MqttTopicService> logger)
    {
        _rep = rep;
        _instanceRep = instanceRep;
        _subscriptionRep = subscriptionRep;
        _logger = logger;
    }

    /// <summary>
    /// 分页查询MQTT主题
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<MqttTopicPageOutput>> GetPageAsync(PageMqttTopicInput input)
    {
        var query = _rep.AsQueryable()
            .LeftJoin<MqttInstance>((t, i) => t.InstanceId == i.Id)
            .WhereIF(input.InstanceId.HasValue, (t, i) => t.InstanceId == input.InstanceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.TopicName), (t, i) => t.TopicName.Contains(input.TopicName!))
            .WhereIF(input.TopicType.HasValue, (t, i) => t.TopicType == input.TopicType)
            .WhereIF(input.QosLevel.HasValue, (t, i) => t.QosLevel == input.QosLevel)
            .WhereIF(input.IsRetained.HasValue, (t, i) => t.IsRetained == input.IsRetained)
            .WhereIF(input.IsEnabled.HasValue, (t, i) => t.IsEnabled == input.IsEnabled)
            .WhereIF(input.IsSystemTopic.HasValue, (t, i) => t.IsSystemTopic == input.IsSystemTopic)
            .WhereIF(input.AllowPublish.HasValue, (t, i) => t.AllowPublish == input.AllowPublish)
            .WhereIF(input.AllowSubscribe.HasValue, (t, i) => t.AllowSubscribe == input.AllowSubscribe)
            .Select((t, i) => new MqttTopicPageOutput
            {
                Id = t.Id,
                InstanceId = t.InstanceId,
                InstanceName = i.InstanceName,
                TopicName = t.TopicName,
                TopicType = t.TopicType,
                QosLevel = t.QosLevel,
                IsRetained = t.IsRetained,
                SubscriberCount = t.SubscriberCount,
                MessageCount = t.MessageCount,
                BytesCount = t.BytesCount,
                LastMessageTime = t.LastMessageTime,
                MaxMessageSize = t.MaxMessageSize,
                MessageExpiry = t.MessageExpiry,
                AllowPublish = t.AllowPublish,
                AllowSubscribe = t.AllowSubscribe,
                IsEnabled = t.IsEnabled,
                IsSystemTopic = t.IsSystemTopic,
                CreateTime = t.CreateTime
            })
            .OrderBy(x => x.CreateTime, OrderByType.Desc);

        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取MQTT主题详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<MqttTopic> GetDetailAsync([Required] long id)
    {
        var topic = await _rep.GetByIdAsync(id);
        if (topic == null)
            throw Oops.Oh("主题不存在");
        return topic;
    }

    /// <summary>
    /// 添加MQTT主题
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task<long> AddAsync(AddMqttTopicInput input)
    {
        // 验证实例是否存在
        var instance = await _instanceRep.GetByIdAsync(input.InstanceId);
        if (instance == null)
            throw Oops.Oh("实例不存在");

        // 验证主题名称格式
        if (!IsValidTopicName(input.TopicName))
            throw Oops.Oh("主题名称格式不正确");

        // 检查主题是否已存在
        var existTopic = await _rep.GetFirstAsync(x => x.TopicName == input.TopicName && x.InstanceId == input.InstanceId);
        if (existTopic != null)
            throw Oops.Oh("该主题已存在");

        var topic = input.Adapt<MqttTopic>();
        
        // 自动检测主题类型
        if (input.TopicType == TopicTypeEnum.Normal)
        {
            topic.TopicType = DetectTopicType(input.TopicName);
        }

        // 初始化统计数据
        topic.SubscriberCount = 0;
        topic.MessageCount = 0;
        topic.BytesCount = 0;

        var newTopic = await _rep.InsertReturnEntityAsync(topic);
        return newTopic.Id;
    }

    /// <summary>
    /// 更新MQTT主题
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task UpdateAsync(UpdateMqttTopicInput input)
    {
        var topic = await _rep.GetByIdAsync(input.Id);
        if (topic == null)
            throw Oops.Oh("主题不存在");

        // 验证主题名称格式
        if (!IsValidTopicName(input.TopicName))
            throw Oops.Oh("主题名称格式不正确");

        // 检查主题名称是否与其他主题冲突
        var existTopic = await _rep.GetFirstAsync(x => x.TopicName == input.TopicName && x.InstanceId == input.InstanceId && x.Id != input.Id);
        if (existTopic != null)
            throw Oops.Oh("该主题名称已被使用");

        var updateTopic = input.Adapt<MqttTopic>();
        
        // 自动检测主题类型
        if (input.TopicType == TopicTypeEnum.Normal)
        {
            updateTopic.TopicType = DetectTopicType(input.TopicName);
        }

        // 保留统计数据
        updateTopic.SubscriberCount = topic.SubscriberCount;
        updateTopic.MessageCount = topic.MessageCount;
        updateTopic.BytesCount = topic.BytesCount;
        updateTopic.LastMessageTime = topic.LastMessageTime;
        updateTopic.LastMessageContent = topic.LastMessageContent;

        await _rep.UpdateAsync(updateTopic);
    }

    /// <summary>
    /// 删除MQTT主题
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task DeleteAsync(DeleteMqttTopicInput input)
    {
        var topic = await _rep.GetByIdAsync(input.Id);
        if (topic == null)
            throw Oops.Oh("主题不存在");

        // 检查是否有订阅者
        var hasSubscriptions = await _subscriptionRep.IsAnyAsync(x => x.TopicName == topic.TopicName && x.InstanceId == topic.InstanceId);
        if (hasSubscriptions)
            throw Oops.Oh("该主题还有订阅者，无法删除");

        await _rep.DeleteAsync(topic);
    }

    /// <summary>
    /// 批量删除MQTT主题
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "BatchDelete")]
    public async Task BatchDeleteAsync(BatchDeleteMqttTopicInput input)
    {
        if (input.Ids == null || !input.Ids.Any())
            throw Oops.Oh("请选择要删除的主题");

        var topics = await _rep.GetListAsync(x => input.Ids.Contains(x.Id));
        
        // 检查是否有订阅者
        foreach (var topic in topics)
        {
            var hasSubscriptions = await _subscriptionRep.IsAnyAsync(x => x.TopicName == topic.TopicName && x.InstanceId == topic.InstanceId);
            if (hasSubscriptions)
                throw Oops.Oh($"主题 {topic.TopicName} 还有订阅者，无法删除");
        }

        await _rep.DeleteByIdsAsync(input.Ids.Cast<dynamic>().ToArray());
    }

    /// <summary>
    /// 启用/禁用MQTT主题
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "ToggleStatus")]
    public async Task ToggleStatusAsync(ToggleTopicStatusInput input)
    {
        var topic = await _rep.GetByIdAsync(input.Id);
        if (topic == null)
            throw Oops.Oh("主题不存在");

        topic.IsEnabled = input.IsEnabled;
        await _rep.UpdateAsync(topic);
    }

    /// <summary>
    /// 获取主题统计信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetStatistics")]
    public async Task<TopicStatisticsOutput> GetStatisticsAsync(GetTopicStatisticsInput input)
    {
        var query = _rep.AsQueryable()
            .WhereIF(input.InstanceId.HasValue, x => x.InstanceId == input.InstanceId)
            .WhereIF(input.TopicType.HasValue, x => x.TopicType == input.TopicType)
            .WhereIF(input.IsEnabled.HasValue, x => x.IsEnabled == input.IsEnabled);

        var totalCount = await query.CountAsync();
        var totalSubscribers = await query.SumAsync(x => x.SubscriberCount);
        var totalMessages = await query.SumAsync(x => x.MessageCount);
        var totalBytes = await query.SumAsync(x => x.BytesCount);

        var topTopics = await query
            .OrderBy(x => x.MessageCount, OrderByType.Desc)
            .Take(10)
            .Select(x => new TopTopicOutput
            {
                TopicName = x.TopicName,
                MessageCount = x.MessageCount,
                SubscriberCount = x.SubscriberCount,
                BytesCount = x.BytesCount
            })
            .ToListAsync();

        return new TopicStatisticsOutput
        {
            TotalTopics = totalCount,
            TotalSubscribers = totalSubscribers,
            TotalMessages = totalMessages,
            TotalBytes = totalBytes,
            TopTopics = topTopics
        };
    }

    /// <summary>
    /// 更新主题统计信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "UpdateStatistics")]
    public async Task UpdateStatisticsAsync(UpdateTopicStatisticsInput input)
    {
        var topic = await _rep.GetFirstAsync(x => x.TopicName == input.TopicName && x.InstanceId == input.InstanceId);
        if (topic == null)
        {
            // 如果主题不存在，自动创建
            topic = new MqttTopic
            {
                InstanceId = input.InstanceId,
                TopicName = input.TopicName,
                TopicType = DetectTopicType(input.TopicName),
                QosLevel = QosLevelEnum.AtMostOnce,
                IsRetained = false,
                SubscriberCount = 0,
                MessageCount = 0,
                BytesCount = 0,
                AllowPublish = true,
                AllowSubscribe = true,
                IsEnabled = true,
                IsSystemTopic = IsSystemTopic(input.TopicName)
            };
            topic = await _rep.InsertReturnEntityAsync(topic);
        }

        // 更新统计信息
        if (input.SubscriberCount.HasValue)
            topic.SubscriberCount = input.SubscriberCount.Value;
        
        if (input.MessageCount.HasValue)
            topic.MessageCount += input.MessageCount.Value;
        
        if (input.BytesCount.HasValue)
            topic.BytesCount += input.BytesCount.Value;
        
        if (!string.IsNullOrWhiteSpace(input.LastMessageContent))
        {
            topic.LastMessageContent = input.LastMessageContent;
            topic.LastMessageTime = DateTime.Now;
        }

        await _rep.UpdateAsync(topic);
    }

    /// <summary>
    /// 获取主题权限
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetPermissions")]
    public async Task<TopicPermissionOutput> GetPermissionsAsync(GetTopicPermissionInput input)
    {
        var topic = await _rep.GetFirstAsync(x => x.TopicName == input.TopicName && x.InstanceId == input.InstanceId);
        if (topic == null)
            throw Oops.Oh("主题不存在");

        return new TopicPermissionOutput
        {
            TopicName = topic.TopicName,
            AllowPublish = topic.AllowPublish,
            AllowSubscribe = topic.AllowSubscribe,
            PublishPermissions = ParsePermissionList(topic.PublishPermissions),
            SubscribePermissions = ParsePermissionList(topic.SubscribePermissions)
        };
    }

    /// <summary>
    /// 设置主题权限
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "SetPermissions")]
    public async Task SetPermissionsAsync(SetTopicPermissionInput input)
    {
        var topic = await _rep.GetFirstAsync(x => x.TopicName == input.TopicName && x.InstanceId == input.InstanceId);
        if (topic == null)
            throw Oops.Oh("主题不存在");

        topic.AllowPublish = input.AllowPublish;
        topic.AllowSubscribe = input.AllowSubscribe;
        topic.PublishPermissions = JsonSerializer.Serialize(input.PublishPermissions ?? new List<string>());
        topic.SubscribePermissions = JsonSerializer.Serialize(input.SubscribePermissions ?? new List<string>());

        await _rep.UpdateAsync(topic);
    }

    /// <summary>
    /// 获取主题选择列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetSelectList")]
    public async Task<List<MqttTopicSelectOutput>> GetSelectListAsync(GetTopicSelectInput input)
    {
        var query = _rep.AsQueryable()
            .WhereIF(input.InstanceId.HasValue, x => x.InstanceId == input.InstanceId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), x => x.TopicName.Contains(input.Keyword!))
            .WhereIF(input.TopicType.HasValue, x => x.TopicType == input.TopicType)
            .Where(x => x.IsEnabled)
            .OrderBy(x => x.TopicName)
            .Take(input.Limit ?? 100);

        return await query.Select(x => new MqttTopicSelectOutput
        {
            Id = x.Id,
            TopicName = x.TopicName,
            TopicType = x.TopicType,
            Description = x.Description
        }).ToListAsync();
    }

    #region 私有方法

    /// <summary>
    /// 验证主题名称格式
    /// </summary>
    /// <param name="topicName"></param>
    /// <returns></returns>
    private bool IsValidTopicName(string topicName)
    {
        if (string.IsNullOrWhiteSpace(topicName))
            return false;

        // MQTT主题名称规则：
        // 1. 不能为空
        // 2. 不能包含空字符（\0）
        // 3. 长度不能超过65535字节
        // 4. 通配符只能在订阅时使用，发布时不能使用
        if (topicName.Contains('\0') || topicName.Length > 65535)
            return false;

        // 检查是否包含非法字符
        var invalidChars = new[] { '\0' };
        return !invalidChars.Any(topicName.Contains);
    }

    /// <summary>
    /// 检测主题类型
    /// </summary>
    /// <param name="topicName"></param>
    /// <returns></returns>
    private TopicTypeEnum DetectTopicType(string topicName)
    {
        if (string.IsNullOrWhiteSpace(topicName))
            return TopicTypeEnum.Normal;

        // 系统主题
        if (IsSystemTopic(topicName))
            return TopicTypeEnum.System;

        // 共享主题
        if (topicName.StartsWith("$share/"))
            return TopicTypeEnum.Shared;

        // 通配符主题
        if (topicName.Contains("+") || topicName.Contains("#"))
            return TopicTypeEnum.Wildcard;

        // 设备影子主题
        if (topicName.Contains("/shadow/"))
            return TopicTypeEnum.DeviceShadow;

        // 事件主题
        if (topicName.Contains("/event/") || topicName.EndsWith("/event"))
            return TopicTypeEnum.Event;

        // 属性主题
        if (topicName.Contains("/property/") || topicName.EndsWith("/property"))
            return TopicTypeEnum.Property;

        // 服务主题
        if (topicName.Contains("/service/") || topicName.EndsWith("/service"))
            return TopicTypeEnum.Service;

        return TopicTypeEnum.Normal;
    }

    /// <summary>
    /// 判断是否为系统主题
    /// </summary>
    /// <param name="topicName"></param>
    /// <returns></returns>
    private bool IsSystemTopic(string topicName)
    {
        if (string.IsNullOrWhiteSpace(topicName))
            return false;

        // MQTT系统主题以$开头
        return topicName.StartsWith("$");
    }

    /// <summary>
    /// 解析权限列表
    /// </summary>
    /// <param name="permissionJson"></param>
    /// <returns></returns>
    private List<string> ParsePermissionList(string? permissionJson)
    {
        if (string.IsNullOrWhiteSpace(permissionJson))
            return new List<string>();

        try
        {
            return JsonSerializer.Deserialize<List<string>>(permissionJson) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    #endregion
}

#region 输入输出模型

/// <summary>
/// 分页查询MQTT主题输入
/// </summary>
public class PageMqttTopicInput : BasePageInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    public long? InstanceId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    public string? TopicName { get; set; }

    /// <summary>
    /// 主题类型
    /// </summary>
    public TopicTypeEnum? TopicType { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public QosLevelEnum? QosLevel { get; set; }

    /// <summary>
    /// 是否保留消息
    /// </summary>
    public bool? IsRetained { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }

    /// <summary>
    /// 是否系统主题
    /// </summary>
    public bool? IsSystemTopic { get; set; }

    /// <summary>
    /// 是否允许发布
    /// </summary>
    public bool? AllowPublish { get; set; }

    /// <summary>
    /// 是否允许订阅
    /// </summary>
    public bool? AllowSubscribe { get; set; }
}

/// <summary>
/// 添加MQTT主题输入
/// </summary>
public class AddMqttTopicInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [Required(ErrorMessage = "主题名称不能为空")]
    [MaxLength(512, ErrorMessage = "主题名称长度不能超过512个字符")]
    public string TopicName { get; set; }

    /// <summary>
    /// 主题类型
    /// </summary>
    public TopicTypeEnum TopicType { get; set; } = TopicTypeEnum.Normal;

    /// <summary>
    /// QoS等级
    /// </summary>
    public QosLevelEnum QosLevel { get; set; } = QosLevelEnum.AtMostOnce;

    /// <summary>
    /// 是否保留消息
    /// </summary>
    public bool IsRetained { get; set; } = false;

    /// <summary>
    /// 最大消息大小（字节）
    /// </summary>
    [Range(1024, 10485760, ErrorMessage = "最大消息大小范围必须在1KB-10MB之间")]
    public int MaxMessageSize { get; set; } = 1048576;

    /// <summary>
    /// 消息过期时间（秒）
    /// </summary>
    [Range(60, 2592000, ErrorMessage = "消息过期时间范围必须在1分钟-30天之间")]
    public int? MessageExpiry { get; set; }

    /// <summary>
    /// 是否允许发布
    /// </summary>
    public bool AllowPublish { get; set; } = true;

    /// <summary>
    /// 是否允许订阅
    /// </summary>
    public bool AllowSubscribe { get; set; } = true;

    /// <summary>
    /// 发布权限（JSON格式）
    /// </summary>
    public string? PublishPermissions { get; set; }

    /// <summary>
    /// 订阅权限（JSON格式）
    /// </summary>
    public string? SubscribePermissions { get; set; }

    /// <summary>
    /// 主题描述
    /// </summary>
    [MaxLength(500, ErrorMessage = "主题描述长度不能超过500个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 主题标签
    /// </summary>
    [MaxLength(200, ErrorMessage = "主题标签长度不能超过200个字符")]
    public string? Tags { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 监控配置
    /// </summary>
    public string? MonitorConfig { get; set; }

    /// <summary>
    /// 告警规则
    /// </summary>
    public string? AlertRules { get; set; }

    /// <summary>
    /// 数据转发规则
    /// </summary>
    public string? ForwardRules { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public string? ExtendedProperties { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 更新MQTT主题输入
/// </summary>
public class UpdateMqttTopicInput : AddMqttTopicInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 删除MQTT主题输入
/// </summary>
public class DeleteMqttTopicInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 批量删除MQTT主题输入
/// </summary>
public class BatchDeleteMqttTopicInput
{
    /// <summary>
    /// 主键ID集合
    /// </summary>
    [Required(ErrorMessage = "主键ID集合不能为空")]
    public List<long> Ids { get; set; }
}

/// <summary>
/// 切换主题状态输入
/// </summary>
public class ToggleTopicStatusInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}

/// <summary>
/// 获取主题统计信息输入
/// </summary>
public class GetTopicStatisticsInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    public long? InstanceId { get; set; }

    /// <summary>
    /// 主题类型
    /// </summary>
    public TopicTypeEnum? TopicType { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }
}

/// <summary>
/// 更新主题统计信息输入
/// </summary>
public class UpdateTopicStatisticsInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [Required(ErrorMessage = "主题名称不能为空")]
    public string TopicName { get; set; }

    /// <summary>
    /// 订阅者数量
    /// </summary>
    public int? SubscriberCount { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public long? MessageCount { get; set; }

    /// <summary>
    /// 字节数量
    /// </summary>
    public long? BytesCount { get; set; }

    /// <summary>
    /// 最后消息内容
    /// </summary>
    public string? LastMessageContent { get; set; }
}

/// <summary>
/// 获取主题权限输入
/// </summary>
public class GetTopicPermissionInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [Required(ErrorMessage = "主题名称不能为空")]
    public string TopicName { get; set; }
}

/// <summary>
/// 设置主题权限输入
/// </summary>
public class SetTopicPermissionInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required(ErrorMessage = "实例ID不能为空")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [Required(ErrorMessage = "主题名称不能为空")]
    public string TopicName { get; set; }

    /// <summary>
    /// 是否允许发布
    /// </summary>
    public bool AllowPublish { get; set; }

    /// <summary>
    /// 是否允许订阅
    /// </summary>
    public bool AllowSubscribe { get; set; }

    /// <summary>
    /// 发布权限列表
    /// </summary>
    public List<string>? PublishPermissions { get; set; }

    /// <summary>
    /// 订阅权限列表
    /// </summary>
    public List<string>? SubscribePermissions { get; set; }
}

/// <summary>
/// 获取主题选择列表输入
/// </summary>
public class GetTopicSelectInput
{
    /// <summary>
    /// 实例ID
    /// </summary>
    public long? InstanceId { get; set; }

    /// <summary>
    /// 关键词
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// 主题类型
    /// </summary>
    public TopicTypeEnum? TopicType { get; set; }

    /// <summary>
    /// 限制数量
    /// </summary>
    [Range(1, 1000, ErrorMessage = "限制数量范围必须在1-1000之间")]
    public int? Limit { get; set; } = 100;
}

/// <summary>
/// MQTT主题分页输出
/// </summary>
public class MqttTopicPageOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 实例ID
    /// </summary>
    public long InstanceId { get; set; }

    /// <summary>
    /// 实例名称
    /// </summary>
    public string InstanceName { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; }

    /// <summary>
    /// 主题类型
    /// </summary>
    public TopicTypeEnum TopicType { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public QosLevelEnum QosLevel { get; set; }

    /// <summary>
    /// 是否保留消息
    /// </summary>
    public bool IsRetained { get; set; }

    /// <summary>
    /// 订阅者数量
    /// </summary>
    public int SubscriberCount { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public long MessageCount { get; set; }

    /// <summary>
    /// 字节数量
    /// </summary>
    public long BytesCount { get; set; }

    /// <summary>
    /// 最后消息时间
    /// </summary>
    public DateTime? LastMessageTime { get; set; }

    /// <summary>
    /// 最大消息大小
    /// </summary>
    public int MaxMessageSize { get; set; }

    /// <summary>
    /// 消息过期时间
    /// </summary>
    public int? MessageExpiry { get; set; }

    /// <summary>
    /// 是否允许发布
    /// </summary>
    public bool AllowPublish { get; set; }

    /// <summary>
    /// 是否允许订阅
    /// </summary>
    public bool AllowSubscribe { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 是否系统主题
    /// </summary>
    public bool IsSystemTopic { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}

/// <summary>
/// 主题统计信息输出
/// </summary>
public class TopicStatisticsOutput
{
    /// <summary>
    /// 主题总数
    /// </summary>
    public int TotalTopics { get; set; }

    /// <summary>
    /// 订阅者总数
    /// </summary>
    public int TotalSubscribers { get; set; }

    /// <summary>
    /// 消息总数
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// 字节总数
    /// </summary>
    public long TotalBytes { get; set; }

    /// <summary>
    /// 热门主题列表
    /// </summary>
    public List<TopTopicOutput> TopTopics { get; set; } = new();
}

/// <summary>
/// 热门主题输出
/// </summary>
public class TopTopicOutput
{
    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public long MessageCount { get; set; }

    /// <summary>
    /// 订阅者数量
    /// </summary>
    public int SubscriberCount { get; set; }

    /// <summary>
    /// 字节数量
    /// </summary>
    public long BytesCount { get; set; }
}

/// <summary>
/// 主题权限输出
/// </summary>
public class TopicPermissionOutput
{
    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; }

    /// <summary>
    /// 是否允许发布
    /// </summary>
    public bool AllowPublish { get; set; }

    /// <summary>
    /// 是否允许订阅
    /// </summary>
    public bool AllowSubscribe { get; set; }

    /// <summary>
    /// 发布权限列表
    /// </summary>
    public List<string> PublishPermissions { get; set; } = new();

    /// <summary>
    /// 订阅权限列表
    /// </summary>
    public List<string> SubscribePermissions { get; set; } = new();
}

/// <summary>
/// MQTT主题选择输出
/// </summary>
public class MqttTopicSelectOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; }

    /// <summary>
    /// 主题类型
    /// </summary>
    public TopicTypeEnum TopicType { get; set; }

    /// <summary>
    /// 主题描述
    /// </summary>
    public string? Description { get; set; }
}

#endregion