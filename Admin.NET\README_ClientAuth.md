# MQTT客户端认证功能说明

## 概述

本插件实现了基于阿里云MQTT签名认证的客户端权限验证功能，支持多种签名算法和完整的认证管理。

## 功能特性

### 1. 阿里云MQTT签名认证
- 支持标准的阿里云MQTT签名认证协议
- Username格式：`Signature|AccessKeyId|InstanceId`
- Password：使用HMAC算法对ClientId进行签名
- 支持多种签名算法：HMAC-SHA1、HMAC-SHA256、HMAC-MD5

### 2. 认证配置管理
- 客户端认证配置的增删改查
- 支持批量操作
- 配置启用/禁用控制
- 过期时间管理

### 3. 安全特性
- AccessKey Secret加密存储
- 登录失败次数限制
- 自动锁定机制
- 缓存优化

### 4. 统计监控
- 认证成功/失败统计
- 客户端状态监控
- 实时数据展示

## API接口

### 认证配置管理

#### 1. 获取认证配置分页列表
```http
POST /api/MqttClientAuth/page
Content-Type: application/json

{
  "clientId": "GID_Test@@@0001",
  "accessKeyId": "LTAI5tFhc8AKGfPdmBTHV2nJ",
  "instanceId": 1,
  "isEnabled": true,
  "page": 1,
  "pageSize": 10
}
```

#### 2. 添加认证配置
```http
POST /api/MqttClientAuth/add
Content-Type: application/json

{
  "clientId": "GID_Test@@@0001",
  "instanceId": 1,
  "accessKeyId": "LTAI5tFhc8AKGfPdmBTHV2nJ",
  "accessKeySecret": "your_access_key_secret_here",
  "signMethod": "hmacsha1",
  "authMode": "Signature",
  "isEnabled": true,
  "remark": "测试客户端认证配置"
}
```

#### 3. 更新认证配置
```http
POST /api/MqttClientAuth/update
Content-Type: application/json

{
  "id": 1,
  "clientId": "GID_Test@@@0001",
  "instanceId": 1,
  "accessKeyId": "LTAI5tFhc8AKGfPdmBTHV2nJ",
  "accessKeySecret": "your_access_key_secret_here",
  "signMethod": "hmacsha256",
  "authMode": "Signature",
  "isEnabled": true,
  "remark": "更新后的配置"
}
```

#### 4. 删除认证配置
```http
POST /api/MqttClientAuth/delete
Content-Type: application/json

{
  "id": 1
}
```

### 签名验证

#### 1. 验证客户端签名
```http
POST /api/MqttClientAuth/validate-signature
Content-Type: application/json

{
  "clientId": "GID_Test@@@0001",
  "username": "Signature|LTAI5tFhc8AKGfPdmBTHV2nJ|mqtt-xxxxx",
  "password": "base64_encoded_signature"
}
```

#### 2. 生成连接参数
```http
POST /api/MqttClientAuth/generate-connection-params
Content-Type: application/json

{
  "authConfigId": 1,
  "clientId": "GID_Test@@@0001"
}
```

#### 3. 测试签名计算
```http
POST /api/MqttClientAuth/test-signature
Content-Type: application/json

{
  "clientId": "GID_Test@@@0001",
  "accessKeySecret": "your_access_key_secret_here",
  "signMethod": "hmacsha1"
}
```

### 管理操作

#### 1. 启用/禁用配置
```http
POST /api/MqttClientAuth/toggle-status/1?enabled=true
```

#### 2. 批量删除
```http
POST /api/MqttClientAuth/batch-delete
Content-Type: application/json

[1, 2, 3]
```

#### 3. 获取统计信息
```http
GET /api/MqttClientAuth/stats?instanceId=1
```

#### 4. 重置失败次数
```http
POST /api/MqttClientAuth/reset-failed-count/1
```

#### 5. 解锁客户端
```http
POST /api/MqttClientAuth/unlock/1
```

## 使用示例

### 1. C# 客户端示例

```csharp
using Admin.NET.Plugin.EMQX.Examples;

// 计算签名
var clientId = "GID_Test@@@0001";
var accessKeySecret = "your_access_key_secret_here";
var signature = MqttClientAuthExample.CalculateSignature(clientId, accessKeySecret, "hmacsha1");

// 生成连接参数
var (username, password) = MqttClientAuthExample.GenerateConnectionParams(
    clientId, 
    "LTAI5tFhc8AKGfPdmBTHV2nJ", 
    accessKeySecret, 
    "mqtt-xxxxx", 
    "hmacsha1");

Console.WriteLine($"Username: {username}");
Console.WriteLine($"Password: {password}");

// 验证认证信息
var (isValid, message) = MqttClientAuthExample.ValidateClientAuth(
    clientId, username, password, accessKeySecret);

Console.WriteLine($"验证结果: {(isValid ? "成功" : "失败")} - {message}");
```

### 2. JavaScript 客户端示例

```javascript
const crypto = require('crypto');

// 计算HMAC-SHA1签名
function calculateSignature(clientId, accessKeySecret) {
    const hmac = crypto.createHmac('sha1', accessKeySecret);
    hmac.update(clientId);
    return hmac.digest('base64');
}

// 生成连接参数
function generateConnectionParams(clientId, accessKeyId, accessKeySecret, instanceId) {
    const username = `Signature|${accessKeyId}|${instanceId}`;
    const password = calculateSignature(clientId, accessKeySecret);
    return { username, password };
}

// 使用示例
const clientId = 'GID_Test@@@0001';
const accessKeyId = 'LTAI5tFhc8AKGfPdmBTHV2nJ';
const accessKeySecret = 'your_access_key_secret_here';
const instanceId = 'mqtt-xxxxx';

const { username, password } = generateConnectionParams(
    clientId, accessKeyId, accessKeySecret, instanceId
);

console.log('Username:', username);
console.log('Password:', password);
```

### 3. Python 客户端示例

```python
import hmac
import hashlib
import base64

def calculate_signature(client_id, access_key_secret, sign_method='hmacsha1'):
    """计算MQTT签名"""
    if sign_method.lower() == 'hmacsha1':
        hash_func = hashlib.sha1
    elif sign_method.lower() == 'hmacsha256':
        hash_func = hashlib.sha256
    elif sign_method.lower() == 'hmacmd5':
        hash_func = hashlib.md5
    else:
        hash_func = hashlib.sha1
    
    signature = hmac.new(
        access_key_secret.encode('utf-8'),
        client_id.encode('utf-8'),
        hash_func
    ).digest()
    
    return base64.b64encode(signature).decode('utf-8')

def generate_connection_params(client_id, access_key_id, access_key_secret, instance_id, sign_method='hmacsha1'):
    """生成MQTT连接参数"""
    username = f"Signature|{access_key_id}|{instance_id}"
    password = calculate_signature(client_id, access_key_secret, sign_method)
    return username, password

# 使用示例
client_id = 'GID_Test@@@0001'
access_key_id = 'LTAI5tFhc8AKGfPdmBTHV2nJ'
access_key_secret = 'your_access_key_secret_here'
instance_id = 'mqtt-xxxxx'

username, password = generate_connection_params(
    client_id, access_key_id, access_key_secret, instance_id
)

print(f'Username: {username}')
print(f'Password: {password}')
```

## 数据库表结构

### mqtt_client_auth 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| Id | bigint | 主键ID |
| ClientId | varchar(128) | 客户端ID |
| InstanceId | bigint | 实例ID |
| AccessKeyId | varchar(64) | AccessKey ID |
| AccessKeySecret | varchar(128) | AccessKey Secret |
| SignMethod | varchar(32) | 签名算法 |
| AuthMode | varchar(32) | 认证模式 |
| IsEnabled | tinyint | 是否启用 |
| LastAuthTime | datetime | 最后认证时间 |
| FailedCount | int | 认证失败次数 |
| ExpireTime | datetime | 过期时间 |
| IsLocked | tinyint | 是否锁定 |
| LockTime | datetime | 锁定时间 |
| FailedLoginCount | int | 登录失败次数 |
| Remark | varchar(500) | 备注 |
| CreateTime | datetime | 创建时间 |
| UpdateTime | datetime | 更新时间 |
| CreateUserId | bigint | 创建用户ID |
| UpdateUserId | bigint | 更新用户ID |
| IsDelete | tinyint | 是否删除 |

## 配置说明

### 签名算法支持
- `hmacsha1`：HMAC-SHA1算法（默认）
- `hmacsha256`：HMAC-SHA256算法
- `hmacmd5`：HMAC-MD5算法

### 认证模式
- `Signature`：阿里云签名认证（默认）
- `Username`：用户名密码认证
- `JWT`：JWT令牌认证
- `Certificate`：证书认证

### 安全配置
- 登录失败超过5次自动锁定
- 锁定时间：30分钟
- AccessKey Secret加密存储
- 支持过期时间控制

## 注意事项

1. **AccessKey Secret安全**：请妥善保管AccessKey Secret，不要在代码中硬编码
2. **签名算法一致性**：客户端和服务端必须使用相同的签名算法
3. **时间同步**：确保客户端和服务端时间同步，避免因时间差导致的认证失败
4. **网络安全**：建议使用TLS/SSL加密传输
5. **缓存更新**：修改认证配置后会自动清除相关缓存

## 故障排除

### 常见问题

1. **签名验证失败**
   - 检查ClientId是否正确
   - 检查AccessKey Secret是否正确
   - 检查签名算法是否一致
   - 检查Username格式是否正确

2. **客户端被锁定**
   - 使用解锁接口解锁客户端
   - 或等待锁定时间过期
   - 检查认证配置是否正确

3. **配置不生效**
   - 检查配置是否启用
   - 检查是否过期
   - 清除相关缓存

### 日志查看

系统会记录详细的认证日志，包括：
- 认证成功/失败记录
- 签名计算过程
- 错误详细信息

可通过日志系统查看具体的错误原因。