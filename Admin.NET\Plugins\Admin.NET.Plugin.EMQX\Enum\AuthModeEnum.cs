// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.ComponentModel;

namespace Admin.NET.Plugin.EMQX.Enum;

/// <summary>
/// MQTT认证模式枚举
/// </summary>
[Description("MQTT认证模式")]
public enum AuthModeEnum
{
    /// <summary>
    /// 用户名密码认证
    /// </summary>
    [Description("用户名密码认证")]
    Username = 1,

    /// <summary>
    /// 阿里云签名认证
    /// </summary>
    [Description("阿里云签名认证")]
    AliyunSignature = 2,

    /// <summary>
    /// JWT认证
    /// </summary>
    [Description("JWT认证")]
    JWT = 3,

    /// <summary>
    /// 证书认证
    /// </summary>
    [Description("证书认证")]
    Certificate = 4,

    /// <summary>
    /// API Key认证
    /// </summary>
    [Description("API Key认证")]
    ApiKey = 5,

    /// <summary>
    /// OAuth2认证
    /// </summary>
    [Description("OAuth2认证")]
    OAuth2 = 6,

    /// <summary>
    /// LDAP认证
    /// </summary>
    [Description("LDAP认证")]
    LDAP = 7,

    /// <summary>
    /// 匿名认证
    /// </summary>
    [Description("匿名认证")]
    Anonymous = 8
}