// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Plugin.EMQX.Enum;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Plugin.EMQX.Entity;

/// <summary>
/// MQTT订阅表
/// </summary>
[SugarTable("mqtt_subscription", "MQTT订阅表")]
[SugarIndex("idx_mqtt_subscription_client", nameof(ClientId), OrderByType.Asc)]
[SugarIndex("idx_mqtt_subscription_topic", nameof(TopicName), OrderByType.Asc)]
[SugarIndex("idx_mqtt_subscription_instance", nameof(InstanceId), OrderByType.Asc)]
[SugarIndex("idx_mqtt_subscription_status", nameof(Status), OrderByType.Asc)]
[SugarIndex("uk_mqtt_subscription_client_topic", $"{nameof(ClientId)},{nameof(TopicName)}", OrderByType.Asc, true)]
public class MqttSubscription : EntityBaseDel
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [SugarColumn(ColumnDescription = "实例ID")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 客户端ID（字符串格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "客户端ID字符串", Length = 128, IsNullable = true)]
    [MaxLength(128)]
    public string? ClientId { get; set; }

    /// <summary>
    /// 客户端表引用ID（外键）
    /// </summary>
    [SugarColumn(ColumnDescription = "客户端表引用ID", IsNullable = true)]
    public long? ClientRefId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [SugarColumn(ColumnDescription = "主题名称", Length = 256)]
    [Required, MaxLength(256)]
    public string TopicName { get; set; }

    /// <summary>
    /// 主题ID（外键引用）
    /// </summary>
    [SugarColumn(ColumnDescription = "主题ID", IsNullable = true)]
    public long? TopicId { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    [SugarColumn(ColumnDescription = "QoS等级")]
    public QosLevelEnum QosLevel { get; set; } = QosLevelEnum.AtMostOnce;

    /// <summary>
    /// 订阅状态
    /// </summary>
    [SugarColumn(ColumnDescription = "订阅状态")]
    public SubscriptionStatusEnum Status { get; set; } = SubscriptionStatusEnum.Active;

    /// <summary>
    /// 订阅时间
    /// </summary>
    [SugarColumn(ColumnDescription = "订阅时间", IsNullable = true)]
    public DateTime? SubscribedAt { get; set; }

    /// <summary>
    /// 取消订阅时间
    /// </summary>
    [SugarColumn(ColumnDescription = "取消订阅时间", IsNullable = true)]
    public DateTime? UnsubscribedAt { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后活动时间", IsNullable = true)]
    public DateTime? LastActivity { get; set; }

    /// <summary>
    /// 接收消息数
    /// </summary>
    [SugarColumn(ColumnDescription = "接收消息数", ColumnName = "MessageCount")]
    public long MessagesReceived { get; set; } = 0;

    /// <summary>
    /// 接收字节数
    /// </summary>
    [SugarColumn(ColumnDescription = "接收字节数")]
    public long BytesReceived { get; set; } = 0;

    /// <summary>
    /// 丢弃消息数
    /// </summary>
    [SugarColumn(ColumnDescription = "丢弃消息数")]
    public long MessagesDropped { get; set; } = 0;

    /// <summary>
    /// 最后消息时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后消息时间", IsNullable = true)]
    public DateTime? LastMessageTime { get; set; }

    /// <summary>
    /// 最后消息ID
    /// </summary>
    [SugarColumn(ColumnDescription = "最后消息ID", Length = 64, IsNullable = true)]
    [MaxLength(64)]
    public string? LastMessageId { get; set; }

    /// <summary>
    /// 是否共享订阅
    /// </summary>
    [SugarColumn(ColumnDescription = "是否共享订阅")]
    public bool IsSharedSubscription { get; set; } = false;

    /// <summary>
    /// 共享组名称
    /// </summary>
    [SugarColumn(ColumnDescription = "共享组名称", Length = 64, IsNullable = true)]
    [MaxLength(64)]
    public string? SharedGroupName { get; set; }

    /// <summary>
    /// 订阅选项（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "订阅选项", ColumnDataType = "text", IsNullable = true)]
    public string? SubscriptionOptions { get; set; }

    /// <summary>
    /// 消息过滤器（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "消息过滤器", ColumnDataType = "text", IsNullable = true)]
    public string? MessageFilter { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 优先级（数字越大优先级越高）
    /// </summary>
    [SugarColumn(ColumnDescription = "优先级")]
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 最大队列长度
    /// </summary>
    [SugarColumn(ColumnDescription = "最大队列长度")]
    public int MaxQueueLength { get; set; } = 1000;

    /// <summary>
    /// 当前队列长度
    /// </summary>
    [SugarColumn(ColumnDescription = "当前队列长度")]
    public int CurrentQueueLength { get; set; } = 0;

    /// <summary>
    /// 消息处理策略
    /// </summary>
    [SugarColumn(ColumnDescription = "消息处理策略")]
    public MessageHandlingStrategyEnum HandlingStrategy { get; set; } = MessageHandlingStrategyEnum.Queue;

    /// <summary>
    /// 重试次数
    /// </summary>
    [SugarColumn(ColumnDescription = "重试次数")]
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    [SugarColumn(ColumnDescription = "最大重试次数")]
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 最后错误信息
    /// </summary>
    [SugarColumn(ColumnDescription = "最后错误信息", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string? LastError { get; set; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    [SugarColumn(ColumnDescription = "订阅时间", IsNullable = true)]
    public DateTime? SubscribeTime { get; set; }

    /// <summary>
    /// 接收消息数
    /// </summary>
    [SugarColumn(ColumnDescription = "接收消息数")]
    public long MessageReceived { get; set; } = 0;

    /// <summary>
    /// 消息丢弃数
    /// </summary>
    [SugarColumn(ColumnDescription = "消息丢弃数")]
    public long MessageDropped { get; set; } = 0;

    /// <summary>
    /// 最后活动时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后活动时间", IsNullable = true)]
    public DateTime? LastActivityTime { get; set; }

    /// <summary>
    /// 最后错误消息
    /// </summary>
    [SugarColumn(ColumnDescription = "最后错误消息", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string? LastErrorMessage { get; set; }

    /// <summary>
    /// 取消订阅时间
    /// </summary>
    [SugarColumn(ColumnDescription = "取消订阅时间", IsNullable = true)]
    public DateTime? UnsubscribeTime { get; set; }

    /// <summary>
    /// 统计信息（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "统计信息", ColumnDataType = "text", IsNullable = true)]
    public string? Statistics { get; set; }

    /// <summary>
    /// 扩展属性（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展属性", ColumnDataType = "text", IsNullable = true)]
    public string? ExtendedProperties { get; set; }

    /// <summary>
    /// 配置信息（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "配置信息", ColumnDataType = "text", IsNullable = true, ColumnName = "ConfigJson")]
    public string? ConfigJson { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string? Remark { get; set; }

    /// <summary>
    /// 所属实例
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(InstanceId))]
    public MqttInstance? Instance { get; set; }

    /// <summary>
    /// 客户端信息
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MqttClient.ClientId), nameof(ClientId))]
    public MqttClient? Client { get; set; }

    /// <summary>
    /// 主题信息
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MqttTopic.TopicName), nameof(TopicName))]
    public MqttTopic? Topic { get; set; }
}