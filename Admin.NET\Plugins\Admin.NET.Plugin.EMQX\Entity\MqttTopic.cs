// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Plugin.EMQX.Enum;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Plugin.EMQX.Entity;

/// <summary>
/// MQTT主题表
/// </summary>
[SugarTable("mqtt_topic", "MQTT主题表")]
[SugarIndex("idx_mqtt_topic_name", nameof(TopicName), OrderByType.Asc)]
[SugarIndex("idx_mqtt_topic_instance", nameof(InstanceId), OrderByType.Asc)]
[SugarIndex("idx_mqtt_topic_type", nameof(TopicType), OrderByType.Asc)]
[SugarIndex("idx_mqtt_topic_enabled", nameof(IsEnabled), OrderByType.Asc)]
public class MqttTopic : EntityBaseDel
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [SugarColumn(ColumnDescription = "实例ID")]
    public long InstanceId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [SugarColumn(ColumnDescription = "主题名称", Length = 256)]
    [Required, MaxLength(256)]
    public string TopicName { get; set; }

    /// <summary>
    /// 主题类型
    /// </summary>
    [SugarColumn(ColumnDescription = "主题类型")]
    public TopicTypeEnum TopicType { get; set; } = TopicTypeEnum.Normal;

    /// <summary>
    /// QoS等级
    /// </summary>
    [SugarColumn(ColumnDescription = "QoS等级")]
    public QosLevelEnum QosLevel { get; set; } = QosLevelEnum.AtMostOnce;

    /// <summary>
    /// 是否保留消息
    /// </summary>
    [SugarColumn(ColumnDescription = "是否保留消息")]
    public bool RetainMessage { get; set; } = false;

    /// <summary>
    /// 订阅数量
    /// </summary>
    [SugarColumn(ColumnDescription = "订阅数量")]
    public int SubscriptionCount { get; set; } = 0;

    /// <summary>
    /// 消息数量
    /// </summary>
    [SugarColumn(ColumnDescription = "消息数量")]
    public long MessageCount { get; set; } = 0;

    /// <summary>
    /// 字节数量
    /// </summary>
    [SugarColumn(ColumnDescription = "字节数量")]
    public long ByteCount { get; set; } = 0;

    /// <summary>
    /// 最后消息时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后消息时间", IsNullable = true)]
    public DateTime? LastMessageTime { get; set; }

    /// <summary>
    /// 最后消息内容（截取前1000字符）
    /// </summary>
    [SugarColumn(ColumnDescription = "最后消息内容", Length = 1000, IsNullable = true)]
    [MaxLength(1000)]
    public string? LastMessageContent { get; set; }

    /// <summary>
    /// 最大消息大小（字节）
    /// </summary>
    [SugarColumn(ColumnDescription = "最大消息大小")]
    public int MaxMessageSize { get; set; } = 1048576; // 1MB

    /// <summary>
    /// 消息过期时间（秒，0表示不过期）
    /// </summary>
    [SugarColumn(ColumnDescription = "消息过期时间")]
    public int MessageExpiry { get; set; } = 0;

    /// <summary>
    /// 是否允许发布
    /// </summary>
    [SugarColumn(ColumnDescription = "是否允许发布")]
    public bool AllowPublish { get; set; } = true;

    /// <summary>
    /// 是否允许订阅
    /// </summary>
    [SugarColumn(ColumnDescription = "是否允许订阅")]
    public bool AllowSubscribe { get; set; } = true;

    /// <summary>
    /// 发布权限（JSON格式，存储允许发布的客户端ID或组ID）
    /// </summary>
    [SugarColumn(ColumnDescription = "发布权限", ColumnDataType = "text", IsNullable = true)]
    public string? PublishPermissions { get; set; }

    /// <summary>
    /// 订阅权限（JSON格式，存储允许订阅的客户端ID或组ID）
    /// </summary>
    [SugarColumn(ColumnDescription = "订阅权限", ColumnDataType = "text", IsNullable = true)]
    public string? SubscribePermissions { get; set; }

    /// <summary>
    /// 是否保留消息
    /// </summary>
    [SugarColumn(ColumnDescription = "是否保留消息")]
    public bool IsRetained { get; set; } = false;

    /// <summary>
    /// 字节数统计
    /// </summary>
    [SugarColumn(ColumnDescription = "字节数统计")]
    public long BytesCount { get; set; } = 0;

    /// <summary>
    /// 主题描述
    /// </summary>
    [SugarColumn(ColumnDescription = "主题描述", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// 主题标签（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "主题标签", ColumnDataType = "text", IsNullable = true)]
    public string? TopicTags { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 是否系统主题
    /// </summary>
    [SugarColumn(ColumnDescription = "是否系统主题")]
    public bool IsSystemTopic { get; set; } = false;

    /// <summary>
    /// 订阅者数量
    /// </summary>
    [SugarColumn(ColumnDescription = "订阅者数量")]
    public int SubscriberCount { get; set; } = 0;

    /// <summary>
    /// 监控配置（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "监控配置", ColumnDataType = "text", IsNullable = true)]
    public string? MonitorConfig { get; set; }

    /// <summary>
    /// 告警规则（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "告警规则", ColumnDataType = "text", IsNullable = true)]
    public string? AlertRules { get; set; }

    /// <summary>
    /// 数据转发规则（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "数据转发规则", ColumnDataType = "text", IsNullable = true)]
    public string? ForwardRules { get; set; }

    /// <summary>
    /// 扩展属性（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展属性", ColumnDataType = "text", IsNullable = true)]
    public string? ExtendedProperties { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string? Remark { get; set; }

    /// <summary>
    /// 所属实例
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(InstanceId))]
    public MqttInstance? Instance { get; set; }

    /// <summary>
    /// 订阅列表
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(MqttSubscription.TopicName), nameof(TopicName))]
    public List<MqttSubscription>? Subscriptions { get; set; }
}