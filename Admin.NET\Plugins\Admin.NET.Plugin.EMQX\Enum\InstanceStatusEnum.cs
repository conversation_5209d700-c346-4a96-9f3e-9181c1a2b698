// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.ComponentModel;

namespace Admin.NET.Plugin.EMQX.Enum;

/// <summary>
/// MQTT实例状态枚举
/// </summary>
[Description("MQTT实例状态")]
public enum InstanceStatusEnum
{
    /// <summary>
    /// 停止
    /// </summary>
    [Description("停止")]
    Stopped = 0,

    /// <summary>
    /// 运行中
    /// </summary>
    [Description("运行中")]
    Running = 1,

    /// <summary>
    /// 活跃
    /// </summary>
    [Description("活跃")]
    Active = 2,

    /// <summary>
    /// 启动中
    /// </summary>
    [Description("启动中")]
    Starting = 3,

    /// <summary>
    /// 停止中
    /// </summary>
    [Description("停止中")]
    Stopping = 4,

    /// <summary>
    /// 重启中
    /// </summary>
    [Description("重启中")]
    Restarting = 5,

    /// <summary>
    /// 异常
    /// </summary>
    [Description("异常")]
    Error = 5,

    /// <summary>
    /// 维护中
    /// </summary>
    [Description("维护中")]
    Maintenance = 6,

    /// <summary>
    /// 未知
    /// </summary>
    [Description("未知")]
    Unknown = 99
}